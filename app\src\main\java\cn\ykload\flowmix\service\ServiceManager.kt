package cn.ykload.flowmix.service

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import cn.ykload.flowmix.notification.NotificationHelper
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 服务管理器
 * 
 * 统一管理Flowmix应用的所有后台服务，包括：
 * 1. 保活服务的启动和停止
 * 2. 服务状态监控
 * 3. 与UI层的状态同步
 * 4. 配置持久化
 */
class ServiceManager private constructor(private val context: Context) {

    companion object {
        private const val TAG = "ServiceManager"
        
        @Volatile
        private var INSTANCE: ServiceManager? = null
        
        /**
         * 获取ServiceManager单例
         */
        fun getInstance(context: Context): ServiceManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ServiceManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }

    private val notificationHelper = NotificationHelper(context)
    private val serviceScope = CoroutineScope(Dispatchers.Main + SupervisorJob())

    // 服务状态
    private val _isKeepAliveServiceRunning = MutableStateFlow(false)
    val isKeepAliveServiceRunning: StateFlow<Boolean> = _isKeepAliveServiceRunning.asStateFlow()

    private val _isFlowmixEnabled = MutableStateFlow(false)
    val isFlowmixEnabled: StateFlow<Boolean> = _isFlowmixEnabled.asStateFlow()

    init {
        Log.d(TAG, "ServiceManager 初始化")

        // 初始化服务状态
        _isKeepAliveServiceRunning.value = FlowmixKeepAliveService.isRunning()

        // 保活服务始终启用，如果未运行则启动
        if (!FlowmixKeepAliveService.isRunning()) {
            Log.d(TAG, "自动启动保活服务")
            startKeepAliveService(false) // 默认以关闭状态启动
        }
    }

    /**
     * 启动保活服务
     */
    fun startKeepAliveService(isFlowmixEnabled: Boolean) {
        serviceScope.launch {
            try {
                Log.d(TAG, "启动保活服务 - Flowmix: ${if (isFlowmixEnabled) "On" else "Off"}")

                FlowmixKeepAliveService.startService(context, isFlowmixEnabled)

                _isKeepAliveServiceRunning.value = true
                _isFlowmixEnabled.value = isFlowmixEnabled

                Log.d(TAG, "保活服务启动成功")
            } catch (e: Exception) {
                Log.e(TAG, "启动保活服务失败", e)
            }
        }
    }

    /**
     * 停止保活服务（仅用于应用完全退出时）
     */
    fun stopKeepAliveService() {
        serviceScope.launch {
            try {
                Log.d(TAG, "停止保活服务")

                FlowmixKeepAliveService.stopService(context)

                _isKeepAliveServiceRunning.value = false
                _isFlowmixEnabled.value = false

                // 取消保活通知
                notificationHelper.cancelNotification(NotificationHelper.NOTIFICATION_KEEP_ALIVE)

                Log.d(TAG, "保活服务停止成功")
            } catch (e: Exception) {
                Log.e(TAG, "停止保活服务失败", e)
            }
        }
    }

    /**
     * 更新Flowmix状态
     */
    fun updateFlowmixStatus(isEnabled: Boolean) {
        serviceScope.launch {
            try {
                Log.d(TAG, "更新Flowmix状态: ${if (isEnabled) "On" else "Off"}")

                _isFlowmixEnabled.value = isEnabled

                // 如果保活服务正在运行，更新服务状态
                if (_isKeepAliveServiceRunning.value) {
                    FlowmixKeepAliveService.updateServiceStatus(context, isEnabled)
                }

                Log.d(TAG, "Flowmix状态更新成功")
            } catch (e: Exception) {
                Log.e(TAG, "更新Flowmix状态失败", e)
            }
        }
    }



    /**
     * 检查服务健康状态
     */
    fun checkServiceHealth() {
        serviceScope.launch {
            val actualServiceRunning = FlowmixKeepAliveService.isRunning()

            if (_isKeepAliveServiceRunning.value != actualServiceRunning) {
                Log.w(TAG, "服务状态不一致，正在同步")
                _isKeepAliveServiceRunning.value = actualServiceRunning

                if (!actualServiceRunning) {
                    // 如果服务未运行，尝试重启（保活服务始终应该运行）
                    Log.w(TAG, "检测到服务异常停止，尝试重启")
                    startKeepAliveService(_isFlowmixEnabled.value)
                }
            }
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        Log.d(TAG, "ServiceManager 清理资源")
        serviceScope.cancel()
    }

    /**
     * 获取服务统计信息
     */
    fun getServiceStats(): ServiceStats {
        return ServiceStats(
            isKeepAliveServiceRunning = _isKeepAliveServiceRunning.value,
            isFlowmixEnabled = _isFlowmixEnabled.value,
            notificationsEnabled = notificationHelper.areNotificationsEnabled()
        )
    }
}

/**
 * 服务统计信息
 */
data class ServiceStats(
    val isKeepAliveServiceRunning: Boolean,
    val isFlowmixEnabled: Boolean,
    val notificationsEnabled: Boolean
)
