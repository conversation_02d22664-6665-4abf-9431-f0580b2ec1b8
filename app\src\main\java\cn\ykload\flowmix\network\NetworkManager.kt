package cn.ykload.flowmix.network

import com.google.gson.Gson
import com.google.gson.GsonBuilder
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

/**
 * 网络管理器
 */
object NetworkManager {

    private const val FR_BASE_URL = "https://fr-api.ykload.com/"
    private const val FLOWSYNC_BASE_URL = "https://flowsync.ykload.com/"

    private val loggingInterceptor = HttpLoggingInterceptor().apply {
        level = HttpLoggingInterceptor.Level.BODY
    }

    private val okHttpClient = OkHttpClient.Builder()
        .addInterceptor(loggingInterceptor)
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .build()

    // 创建自定义的 Gson 实例，增强泛型类型处理
    private val gson: Gson = GsonBuilder()
        .setLenient() // 宽松模式，更好地处理不规范的JSON
        .serializeNulls() // 序列化null值
        .create()

    // 频响数据API的Retrofit实例
    private val frRetrofit = Retrofit.Builder()
        .baseUrl(FR_BASE_URL)
        .client(okHttpClient)
        .addConverterFactory(GsonConverterFactory.create(gson))
        .build()

    // FlowSync API的Retrofit实例
    private val flowSyncRetrofit = Retrofit.Builder()
        .baseUrl(FLOWSYNC_BASE_URL)
        .client(okHttpClient)
        .addConverterFactory(GsonConverterFactory.create(gson))
        .build()

    val frequencyResponseApi: FrequencyResponseApi = frRetrofit.create(FrequencyResponseApi::class.java)
    val flowSyncApi: FlowSyncApi = flowSyncRetrofit.create(FlowSyncApi::class.java)

    /**
     * 获取用于WebSocket连接的OkHttpClient
     */
    fun getWebSocketClient(): OkHttpClient = okHttpClient

    /**
     * 获取配置好的Gson实例
     */
    fun getGson(): Gson = gson
}
