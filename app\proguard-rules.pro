# FlowMix ProGuard Rules - 优化版本 v2.0
# 针对WebSocket连接问题进行精确配置，平衡混淆效果和功能稳定性

# 调试信息保留（可选，发布时可注释掉以进一步减小体积）
# -keepattributes SourceFile,LineNumberTable
# -renamesourcefileattribute SourceFile

# 核心属性保护 - 确保序列化和反射正常工作
-keepattributes Signature
-keepattributes *Annotation*
-keepattributes RuntimeVisibleAnnotations
-keepattributes RuntimeVisibleParameterAnnotations
-keepattributes EnclosingMethod
-keepattributes InnerClasses

# 优化设置 - 激进优化但保护关键功能
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 5
-allowaccessmodification
-dontpreverify

# ============================================================================
# 网络库配置 - 精确保护关键组件
# ============================================================================

# Retrofit - 保护核心功能，允许适度混淆
-dontwarn retrofit2.**
-keep,allowobfuscation,allowshrinking class retrofit2.Retrofit
-keep,allowobfuscation,allowshrinking interface retrofit2.Call
-keep,allowobfuscation,allowshrinking class retrofit2.Response
-keep,allowobfuscation,allowshrinking class retrofit2.Callback
-keep,allowobfuscation,allowshrinking class kotlin.coroutines.Continuation

# OkHttp - WebSocket核心保护（关键！）
-dontwarn okhttp3.**
-dontwarn okio.**
-dontwarn javax.annotation.**
-keep class okhttp3.WebSocket { *; }
-keep class okhttp3.WebSocketListener { *; }
-keep class okhttp3.Request { *; }
-keep class okhttp3.Response { *; }
-keep class okhttp3.OkHttpClient { *; }
-keep class okhttp3.Request$Builder { *; }
-keepnames class okhttp3.internal.publicsuffix.PublicSuffixDatabase

# Gson - 精确序列化保护
-dontwarn sun.misc.**
-keep class com.google.gson.Gson { *; }
-keep class com.google.gson.GsonBuilder { *; }
-keep class com.google.gson.JsonSyntaxException { *; }
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# 保护序列化注解字段
-keepclassmembers,allowobfuscation class * {
    @com.google.gson.annotations.SerializedName <fields>;
    @com.google.gson.annotations.Expose <fields>;
}

# Kotlin协程 - 仅保护WebSocket相关协程
-keep class kotlinx.coroutines.CoroutineScope { *; }
-keep class kotlinx.coroutines.Job { *; }
-keep class kotlinx.coroutines.flow.StateFlow { *; }
-keep class kotlinx.coroutines.flow.MutableStateFlow { *; }
-keep class kotlinx.coroutines.flow.Flow { *; }
-dontwarn kotlinx.coroutines.**

# ============================================================================
# 应用数据模型保护 - 精确保护关键数据类
# ============================================================================

# WebSocket消息类 - 关键！必须完全保护
-keep class cn.ykload.flowmix.data.WebSocketMessage { *; }
-keep class * extends cn.ykload.flowmix.data.WebSocketMessage { *; }
-keep class cn.ykload.flowmix.data.AuthMessage { *; }
-keep class cn.ykload.flowmix.data.ClientInfo { *; }
-keep class cn.ykload.flowmix.data.AuthSuccessMessage { *; }
-keep class cn.ykload.flowmix.data.AuthFailedMessage { *; }
-keep class cn.ykload.flowmix.data.CloudConfigMessage { *; }
-keep class cn.ykload.flowmix.data.SyncToCloudMessage { *; }
-keep class cn.ykload.flowmix.data.SyncSuccessMessage { *; }
-keep class cn.ykload.flowmix.data.SyncFailedMessage { *; }
-keep class cn.ykload.flowmix.data.ConfigUpdatedMessage { *; }
-keep class cn.ykload.flowmix.data.GetCloudConfigMessage { *; }
-keep class cn.ykload.flowmix.data.PingMessage { *; }
-keep class cn.ykload.flowmix.data.ErrorMessage { *; }

# 云端数据模型
-keep class cn.ykload.flowmix.data.CloudDeviceConfigCollection { *; }
-keep class cn.ykload.flowmix.data.CloudDeviceConfig { *; }
-keep class cn.ykload.flowmix.data.AuthInfo { *; }
-keep class cn.ykload.flowmix.data.LoginRequest { *; }
-keep class cn.ykload.flowmix.data.LoginResponse { *; }

# 设备配置相关 - 允许字段名混淆但保护结构
-keep,allowobfuscation class cn.ykload.flowmix.data.DeviceConfig { *; }
-keep,allowobfuscation class cn.ykload.flowmix.data.DeviceConfigCollection { *; }

# 频响数据模型 - 关键！必须完全保护
-keep class cn.ykload.flowmix.data.DataSource { *; }
-keep class cn.ykload.flowmix.data.Brand { *; }
-keep class cn.ykload.flowmix.data.Headphone { *; }
-keep class cn.ykload.flowmix.data.MeasurementCondition { *; }
-keep class cn.ykload.flowmix.data.HeadphoneFrequencyData { *; }
-keep class cn.ykload.flowmix.data.ApiResponse { *; }
-keep class cn.ykload.flowmix.data.TargetCurve { *; }
-keep class cn.ykload.flowmix.data.TargetCurveData { *; }

# API响应类型别名 - 完全保护
-keep class cn.ykload.flowmix.data.DataSourcesResponse { *; }
-keep class cn.ykload.flowmix.data.BrandsResponse { *; }
-keep class cn.ykload.flowmix.data.HeadphonesResponse { *; }
-keep class cn.ykload.flowmix.data.FrequencyDataResponse { *; }
-keep class cn.ykload.flowmix.data.TargetCurvesResponse { *; }
-keep class cn.ykload.flowmix.data.TargetCurveDataResponse { *; }

# 网络接口 - 保护方法签名
-keep interface cn.ykload.flowmix.network.** { *; }

# 枚举类保护
-keep enum cn.ykload.flowmix.data.WebSocketState { *; }
-keep enum cn.ykload.flowmix.data.CloudSyncStatus { *; }
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# ============================================================================
# WebSocket和同步管理器保护 - 关键组件
# ============================================================================

# WebSocket管理器 - 完全保护关键方法
-keep class cn.ykload.flowmix.sync.WebSocketManager {
    public *;
    private kotlinx.coroutines.flow.MutableStateFlow _connectionState;
    private okhttp3.WebSocket webSocket;
    private cn.ykload.flowmix.sync.WebSocketMessageListener messageListener;
}

# WebSocket监听器接口
-keep interface cn.ykload.flowmix.sync.WebSocketMessageListener { *; }

# 云端同步管理器 - 保护关键状态和方法
-keep class cn.ykload.flowmix.sync.CloudSyncManager {
    public *;
    private kotlinx.coroutines.flow.MutableStateFlow _syncStatus;
    private kotlinx.coroutines.flow.MutableStateFlow _errorMessage;
}

# 认证管理器
-keep class cn.ykload.flowmix.auth.AuthManager { *; }

# 网络管理器 - 保护静态方法
-keep class cn.ykload.flowmix.network.NetworkManager {
    public static okhttp3.OkHttpClient getWebSocketClient();
    public static com.google.gson.Gson getGson();
}

# 内部类和匿名类保护
-keep class cn.ykload.flowmix.sync.WebSocketManager$* { *; }
-keep class cn.ykload.flowmix.sync.CloudSyncManager$* { *; }

# FlowSync WebSocket 相关类保护
-keep class cn.ykload.flowmix.data.WebSocketMessage { *; }
-keep class cn.ykload.flowmix.data.AuthMessage { *; }
-keep class cn.ykload.flowmix.data.AuthSuccessMessage { *; }
-keep class cn.ykload.flowmix.data.AuthFailedMessage { *; }
-keep class cn.ykload.flowmix.data.GetCloudConfigMessage { *; }
-keep class cn.ykload.flowmix.data.CloudConfigMessage { *; }
-keep class cn.ykload.flowmix.data.SyncToCloudMessage { *; }
-keep class cn.ykload.flowmix.data.SyncSuccessMessage { *; }
-keep class cn.ykload.flowmix.data.SyncFailedMessage { *; }
-keep class cn.ykload.flowmix.data.ConfigUpdatedMessage { *; }
-keep class cn.ykload.flowmix.data.PingMessage { *; }
-keep class cn.ykload.flowmix.data.PongMessage { *; }
-keep class cn.ykload.flowmix.data.ErrorMessage { *; }
-keep class cn.ykload.flowmix.data.ClientInfo { *; }
-keep class cn.ykload.flowmix.data.ConfigUpdateData { *; }
-keep class cn.ykload.flowmix.data.CloudDeviceConfigCollection { *; }
-keep class cn.ykload.flowmix.data.CloudDeviceConfig { *; }

# 保护 sealed class 的继承关系
-keepclassmembers class cn.ykload.flowmix.data.WebSocketMessage {
    <fields>;
    <methods>;
}
-keepclassmembers class * extends cn.ykload.flowmix.data.WebSocketMessage {
    <fields>;
    <methods>;
}

# 保护 WebSocket 和同步管理器
-keep class cn.ykload.flowmix.sync.WebSocketManager { *; }
-keep class cn.ykload.flowmix.sync.CloudSyncManager { *; }
-keep class cn.ykload.flowmix.sync.WebSocketMessageListener { *; }
-keep class cn.ykload.flowmix.auth.AuthManager { *; }

# 保护 OkHttp WebSocket 相关类
-keep class okhttp3.WebSocket { *; }
-keep class okhttp3.WebSocketListener { *; }
-keep class okhttp3.Request { *; }
-keep class okhttp3.Response { *; }
-keep class okhttp3.Request$Builder { *; }

# 保护 WebSocket 内部类和匿名类
-keepclassmembers class cn.ykload.flowmix.sync.WebSocketManager {
    *;
}
-keepclassmembers class cn.ykload.flowmix.sync.WebSocketManager$* {
    *;
}

# 保护 Kotlin 协程相关的 WebSocket 操作
-keep class kotlinx.coroutines.Job { *; }
-keep class kotlinx.coroutines.flow.MutableStateFlow { *; }
-keep class kotlinx.coroutines.flow.StateFlow { *; }

# 保护网络状态枚举
-keep enum cn.ykload.flowmix.data.WebSocketState { *; }
-keep enum cn.ykload.flowmix.data.CloudSyncStatus { *; }

# 保护 MessageTypeWrapper 内部类
-keep class cn.ykload.flowmix.sync.WebSocketManager$MessageTypeWrapper { *; }

# ============================================================================
# 系统和第三方库优化配置
# ============================================================================

# SSL/TLS 网络安全 - 仅保护关键类
-keep class javax.net.ssl.SSLSocketFactory { *; }
-keep class javax.net.ssl.HttpsURLConnection { *; }
-dontwarn javax.net.ssl.**
-dontwarn java.security.cert.**

# 网络异常 - 保护关键异常类
-keep class java.io.IOException { *; }
-keep class java.net.SocketException { *; }
-keep class java.net.ConnectException { *; }

# Android系统组件 - 允许混淆但保护关键功能
-dontwarn android.**

# Compose - 精简保护
-dontwarn androidx.compose.**

# ============================================================================
# 性能优化配置
# ============================================================================

# 移除调试日志（Release版本）
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int d(...);
}

# 保留错误和警告日志
-keep class android.util.Log {
    public static int w(...);
    public static int e(...);
    public static int i(...);
}

# ============================================================================
# 最终优化设置
# ============================================================================

# 不混淆异常类名，便于调试
-keep class * extends java.lang.Exception {
    <init>(...);
    public java.lang.String getMessage();
}

# 保护应用入口点
-keep class cn.ykload.flowmix.MainActivity { *; }
-keep class cn.ykload.flowmix.FlowmixApplication { *; }