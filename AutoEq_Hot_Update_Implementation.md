# AutoEq热更新功能实现文档

## 概述

本文档描述了Flowmix App中AutoEq配置热更新功能的实现。该功能允许在Flowmix总开关开启时，当AutoEq配置更新时不重载整个AutoEq应用，而是仅调节变化的频段，实现无缝的音频效果更新。

## 问题背景

### 原有实现的问题

在优化前，`AudioEffectManager.applyAutoEq()`方法总是执行以下步骤：

1. 调用`releaseEffect()`释放整个DynamicsProcessing实例
2. 重新创建DynamicsProcessing配置
3. 创建新的DynamicsProcessing实例
4. 重新启用效果器

这种方式存在以下问题：
- **音频中断**：完全重载会导致短暂的音频效果中断
- **性能开销**：重新创建整个效果器比更新单个频段消耗更多资源
- **用户体验差**：频繁的配置变更会导致明显的音频跳跃

### 热更新的优势

- **无缝更新**：只更新变化的频段，避免音频中断
- **性能优化**：减少不必要的资源创建和释放
- **更好的用户体验**：配置变更时音频效果平滑过渡

## 技术实现

### 核心API支持

Android DynamicsProcessing API提供了以下方法支持动态更新：

```kotlin
// 设置单个频段
fun setPreEqBandByChannelIndex(channelIndex: Int, band: Int, preEqBand: DynamicsProcessing.EqBand)

// 获取单个频段
fun getPreEqBandByChannelIndex(channelIndex: Int, band: Int): DynamicsProcessing.EqBand
```

### 实现架构

#### 1. 差异检测 (`detectEqDifferences`)

```kotlin
private fun detectEqDifferences(newEqData: AutoEqData): List<Int>?
```

**功能**：检测新旧AutoEq配置之间的差异

**返回值**：
- `List<Int>`：需要更新的频段索引列表
- `null`：需要完全重载（频段数量或频率结构变化）

**检测逻辑**：
1. 比较频段数量，不同则返回null
2. 逐个比较频段的频率，不同则返回null
3. 比较频段增益，差异超过0.01dB则加入变更列表

#### 2. 热更新实现 (`hotUpdateAutoEq`)

```kotlin
private fun hotUpdateAutoEq(newEqData: AutoEqData, changedBands: List<Int>): Boolean
```

**功能**：仅更新变化的频段

**实现步骤**：
1. 验证DynamicsProcessing实例和效果器状态
2. 遍历需要更新的频段索引
3. 为每个频段创建新的`DynamicsProcessing.EqBand`
4. 使用`setPreEqBandByChannelIndex`更新所有声道的对应频段
5. 更新内部状态

#### 3. 智能应用策略 (`applyAutoEq`)

```kotlin
fun applyAutoEq(autoEqData: AutoEqData, forceFullReload: Boolean = false): Boolean
```

**决策逻辑**：
1. 如果强制完全重载或效果器未初始化 → 完全重载
2. 检测配置差异：
   - 返回null（结构变化） → 完全重载
   - 返回空列表（无变化） → 跳过更新
   - 返回变更列表 → 尝试热更新
3. 热更新失败 → 回退到完全重载

### 错误处理和回退机制

#### 热更新失败场景

1. **DynamicsProcessing实例为null**
2. **效果器未启用**
3. **频段索引超出范围**
4. **频段参数无效**
5. **API调用异常**

#### 回退策略

当热更新失败时，系统自动回退到完全重载：

```kotlin
val hotUpdateSuccess = hotUpdateAutoEq(autoEqData, changedBands)
if (hotUpdateSuccess) {
    Log.d(TAG, "AutoEq热更新成功")
    return true
} else {
    Log.w(TAG, "热更新失败，回退到完全重载")
    return applyAutoEqFullReload(autoEqData)
}
```

## 使用场景

### 1. 热更新场景

- **Flowmix开关已开启**
- **频段数量不变**
- **频率结构不变**
- **仅增益值发生变化**

### 2. 完全重载场景

- **Flowmix开关未开启**（强制重载）
- **频段数量变化**
- **频率结构变化**
- **热更新失败**

## 性能优化

### 日志级别优化

- **DEBUG级别**：关键流程和决策信息
- **VERBOSE级别**：详细的频段更新信息
- **ERROR级别**：错误和异常信息

### 参数验证

在热更新前进行严格的参数验证：
- 频段索引范围检查
- 频率和增益值有效性检查
- 效果器状态验证

## 测试验证

### 测试场景

1. **部分频段增益变化** → 热更新
2. **频段数量变化** → 完全重载
3. **频率结构变化** → 完全重载
4. **配置无变化** → 跳过更新
5. **微小差异容忍** → 跳过更新（<0.01dB）

### 测试文件

`app/src/test/java/cn/ykload/flowmix/audio/AudioEffectManagerHotUpdateTest.kt`

## 配置参数

### 差异容忍度

```kotlin
// 增益允许0.01dB的差异
if (kotlin.math.abs(thisBand.gain - otherBand.gain) > 0.01f) return false
```

### 声道数量

```kotlin
private const val CHANNEL_COUNT = 2 // 立体声
```

## 注意事项

1. **Android API限制**：热更新功能依赖Android API 28+的DynamicsProcessing
2. **设备兼容性**：部分设备可能不支持DynamicsProcessing
3. **线程安全**：所有操作都在主线程的ViewModel协程中执行
4. **内存管理**：热更新避免了频繁的对象创建和释放

## 未来改进

1. **批量更新优化**：一次性更新多个频段
2. **动画过渡**：为频段变化添加平滑过渡动画
3. **性能监控**：添加热更新性能指标统计
4. **A/B测试**：对比热更新和完全重载的用户体验差异
