# The proguard configuration file for the following section is D:\Projects\flowmix\app\build\intermediates\aapt_proguard_file\release\processReleaseResources\aapt_rules.txt
-keep class androidx.core.app.CoreComponentFactory { <init>(); }
-keep class androidx.profileinstaller.ProfileInstallReceiver { <init>(); }
-keep class androidx.startup.InitializationProvider { <init>(); }
-keep class cn.ykload.flowmix.EqualizerActivity { <init>(); }
-keep class cn.ykload.flowmix.MainActivity { <init>(); }
-keep class cn.ykload.flowmix.receiver.BootReceiver { <init>(); }
-keep class cn.ykload.flowmix.service.FlowmixKeepAliveService { <init>(); }

# End of content from D:\Projects\flowmix\app\build\intermediates\aapt_proguard_file\release\processReleaseResources\aapt_rules.txt
# The proguard configuration file for the following section is D:\Projects\flowmix\app\build\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.12.0
# This is a configuration file for ProGuard.
# http://proguard.sourceforge.net/index.html#manual/usage.html
#
# Starting with version 2.2 of the Android plugin for Gradle, this file is distributed together with
# the plugin and unpacked at build-time. The files in $ANDROID_HOME are no longer maintained and
# will be ignored by new version of the Android plugin for Gradle.

# Optimizations: If you don't want to optimize, use the proguard-android.txt configuration file
# instead of this one, which turns off the optimization flags.
-allowaccessmodification

# Preserve some attributes that may be required for reflection.
-keepattributes AnnotationDefault,
                EnclosingMethod,
                InnerClasses,
                RuntimeVisibleAnnotations,
                RuntimeVisibleParameterAnnotations,
                RuntimeVisibleTypeAnnotations,
                Signature

-keep public class com.google.vending.licensing.ILicensingService
-keep public class com.android.vending.licensing.ILicensingService
-keep public class com.google.android.vending.licensing.ILicensingService
-dontnote com.android.vending.licensing.ILicensingService
-dontnote com.google.vending.licensing.ILicensingService
-dontnote com.google.android.vending.licensing.ILicensingService

# For native methods, see https://www.guardsquare.com/manual/configuration/examples#native
-keepclasseswithmembernames,includedescriptorclasses class * {
    native <methods>;
}

# Keep setters in Views so that animations can still work.
-keepclassmembers public class * extends android.view.View {
    void set*(***);
    *** get*();
}

# We want to keep methods in Activity that could be used in the XML attribute onClick.
-keepclassmembers class * extends android.app.Activity {
    public void *(android.view.View);
}

# For enumeration classes, see https://www.guardsquare.com/manual/configuration/examples#enumerations
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keepclassmembers class * implements android.os.Parcelable {
    public static final ** CREATOR;
}

# Preserve annotated Javascript interface methods.
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# The support libraries contains references to newer platform versions.
# Don't warn about those in case this app is linking against an older
# platform version. We know about them, and they are safe.
-dontnote android.support.**
-dontnote androidx.**
-dontwarn android.support.**
-dontwarn androidx.**

# Understand the @Keep support annotation.
-keep class android.support.annotation.Keep

-keep @android.support.annotation.Keep class * {*;}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <init>(...);
}

# These classes are duplicated between android.jar and org.apache.http.legacy.jar.
-dontnote org.apache.http.**
-dontnote android.net.http.**

# These classes are duplicated between android.jar and core-lambda-stubs.jar.
-dontnote java.lang.invoke.**

# End of content from D:\Projects\flowmix\app\build\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.12.0
# The proguard configuration file for the following section is D:\Projects\flowmix\app\proguard-rules.pro
# FlowMix ProGuard Rules - 优化版本 v2.0
# 针对WebSocket连接问题进行精确配置，平衡混淆效果和功能稳定性

# 调试信息保留（可选，发布时可注释掉以进一步减小体积）
# -keepattributes SourceFile,LineNumberTable
# -renamesourcefileattribute SourceFile

# 核心属性保护 - 确保序列化和反射正常工作
-keepattributes Signature
-keepattributes *Annotation*
-keepattributes RuntimeVisibleAnnotations
-keepattributes RuntimeVisibleParameterAnnotations
-keepattributes EnclosingMethod
-keepattributes InnerClasses

# 优化设置 - 激进优化但保护关键功能
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 5
-allowaccessmodification
-dontpreverify

# ============================================================================
# 网络库配置 - 精确保护关键组件
# ============================================================================

# Retrofit - 保护核心功能，允许适度混淆
-dontwarn retrofit2.**
-keep,allowobfuscation,allowshrinking class retrofit2.Retrofit
-keep,allowobfuscation,allowshrinking interface retrofit2.Call
-keep,allowobfuscation,allowshrinking class retrofit2.Response
-keep,allowobfuscation,allowshrinking class retrofit2.Callback
-keep,allowobfuscation,allowshrinking class kotlin.coroutines.Continuation

# OkHttp - WebSocket核心保护（关键！）
-dontwarn okhttp3.**
-dontwarn okio.**
-dontwarn javax.annotation.**
-keep class okhttp3.WebSocket { *; }
-keep class okhttp3.WebSocketListener { *; }
-keep class okhttp3.Request { *; }
-keep class okhttp3.Response { *; }
-keep class okhttp3.OkHttpClient { *; }
-keep class okhttp3.Request$Builder { *; }
-keepnames class okhttp3.internal.publicsuffix.PublicSuffixDatabase

# Gson - 精确序列化保护
-dontwarn sun.misc.**
-keep class com.google.gson.Gson { *; }
-keep class com.google.gson.GsonBuilder { *; }
-keep class com.google.gson.JsonSyntaxException { *; }
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# 保护序列化注解字段
-keepclassmembers,allowobfuscation class * {
    @com.google.gson.annotations.SerializedName <fields>;
    @com.google.gson.annotations.Expose <fields>;
}

# Kotlin协程 - 仅保护WebSocket相关协程
-keep class kotlinx.coroutines.CoroutineScope { *; }
-keep class kotlinx.coroutines.Job { *; }
-keep class kotlinx.coroutines.flow.StateFlow { *; }
-keep class kotlinx.coroutines.flow.MutableStateFlow { *; }
-keep class kotlinx.coroutines.flow.Flow { *; }
-dontwarn kotlinx.coroutines.**

# ============================================================================
# 应用数据模型保护 - 精确保护关键数据类
# ============================================================================

# WebSocket消息类 - 关键！必须完全保护
-keep class cn.ykload.flowmix.data.WebSocketMessage { *; }
-keep class * extends cn.ykload.flowmix.data.WebSocketMessage { *; }
-keep class cn.ykload.flowmix.data.AuthMessage { *; }
-keep class cn.ykload.flowmix.data.ClientInfo { *; }
-keep class cn.ykload.flowmix.data.AuthSuccessMessage { *; }
-keep class cn.ykload.flowmix.data.AuthFailedMessage { *; }
-keep class cn.ykload.flowmix.data.CloudConfigMessage { *; }
-keep class cn.ykload.flowmix.data.SyncToCloudMessage { *; }
-keep class cn.ykload.flowmix.data.SyncSuccessMessage { *; }
-keep class cn.ykload.flowmix.data.SyncFailedMessage { *; }
-keep class cn.ykload.flowmix.data.ConfigUpdatedMessage { *; }
-keep class cn.ykload.flowmix.data.GetCloudConfigMessage { *; }
-keep class cn.ykload.flowmix.data.PingMessage { *; }
-keep class cn.ykload.flowmix.data.ErrorMessage { *; }

# 云端数据模型
-keep class cn.ykload.flowmix.data.CloudDeviceConfigCollection { *; }
-keep class cn.ykload.flowmix.data.CloudDeviceConfig { *; }
-keep class cn.ykload.flowmix.data.AuthInfo { *; }
-keep class cn.ykload.flowmix.data.LoginRequest { *; }
-keep class cn.ykload.flowmix.data.LoginResponse { *; }

# 设备配置相关 - 允许字段名混淆但保护结构
-keep,allowobfuscation class cn.ykload.flowmix.data.DeviceConfig { *; }
-keep,allowobfuscation class cn.ykload.flowmix.data.DeviceConfigCollection { *; }

# 频响数据模型 - 关键！必须完全保护
-keep class cn.ykload.flowmix.data.DataSource { *; }
-keep class cn.ykload.flowmix.data.Brand { *; }
-keep class cn.ykload.flowmix.data.Headphone { *; }
-keep class cn.ykload.flowmix.data.MeasurementCondition { *; }
-keep class cn.ykload.flowmix.data.HeadphoneFrequencyData { *; }
-keep class cn.ykload.flowmix.data.ApiResponse { *; }
-keep class cn.ykload.flowmix.data.TargetCurve { *; }
-keep class cn.ykload.flowmix.data.TargetCurveData { *; }

# API响应类型别名 - 完全保护
-keep class cn.ykload.flowmix.data.DataSourcesResponse { *; }
-keep class cn.ykload.flowmix.data.BrandsResponse { *; }
-keep class cn.ykload.flowmix.data.HeadphonesResponse { *; }
-keep class cn.ykload.flowmix.data.FrequencyDataResponse { *; }
-keep class cn.ykload.flowmix.data.TargetCurvesResponse { *; }
-keep class cn.ykload.flowmix.data.TargetCurveDataResponse { *; }

# 网络接口 - 保护方法签名
-keep interface cn.ykload.flowmix.network.** { *; }

# 枚举类保护
-keep enum cn.ykload.flowmix.data.WebSocketState { *; }
-keep enum cn.ykload.flowmix.data.CloudSyncStatus { *; }
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# ============================================================================
# WebSocket和同步管理器保护 - 关键组件
# ============================================================================

# WebSocket管理器 - 完全保护关键方法
-keep class cn.ykload.flowmix.sync.WebSocketManager {
    public *;
    private kotlinx.coroutines.flow.MutableStateFlow _connectionState;
    private okhttp3.WebSocket webSocket;
    private cn.ykload.flowmix.sync.WebSocketMessageListener messageListener;
}

# WebSocket监听器接口
-keep interface cn.ykload.flowmix.sync.WebSocketMessageListener { *; }

# 云端同步管理器 - 保护关键状态和方法
-keep class cn.ykload.flowmix.sync.CloudSyncManager {
    public *;
    private kotlinx.coroutines.flow.MutableStateFlow _syncStatus;
    private kotlinx.coroutines.flow.MutableStateFlow _errorMessage;
}

# 认证管理器
-keep class cn.ykload.flowmix.auth.AuthManager { *; }

# 网络管理器 - 保护静态方法
-keep class cn.ykload.flowmix.network.NetworkManager {
    public static okhttp3.OkHttpClient getWebSocketClient();
    public static com.google.gson.Gson getGson();
}

# 内部类和匿名类保护
-keep class cn.ykload.flowmix.sync.WebSocketManager$* { *; }
-keep class cn.ykload.flowmix.sync.CloudSyncManager$* { *; }

# FlowSync WebSocket 相关类保护
-keep class cn.ykload.flowmix.data.WebSocketMessage { *; }
-keep class cn.ykload.flowmix.data.AuthMessage { *; }
-keep class cn.ykload.flowmix.data.AuthSuccessMessage { *; }
-keep class cn.ykload.flowmix.data.AuthFailedMessage { *; }
-keep class cn.ykload.flowmix.data.GetCloudConfigMessage { *; }
-keep class cn.ykload.flowmix.data.CloudConfigMessage { *; }
-keep class cn.ykload.flowmix.data.SyncToCloudMessage { *; }
-keep class cn.ykload.flowmix.data.SyncSuccessMessage { *; }
-keep class cn.ykload.flowmix.data.SyncFailedMessage { *; }
-keep class cn.ykload.flowmix.data.ConfigUpdatedMessage { *; }
-keep class cn.ykload.flowmix.data.PingMessage { *; }
-keep class cn.ykload.flowmix.data.PongMessage { *; }
-keep class cn.ykload.flowmix.data.ErrorMessage { *; }
-keep class cn.ykload.flowmix.data.ClientInfo { *; }
-keep class cn.ykload.flowmix.data.ConfigUpdateData { *; }
-keep class cn.ykload.flowmix.data.CloudDeviceConfigCollection { *; }
-keep class cn.ykload.flowmix.data.CloudDeviceConfig { *; }

# 保护 sealed class 的继承关系
-keepclassmembers class cn.ykload.flowmix.data.WebSocketMessage {
    <fields>;
    <methods>;
}
-keepclassmembers class * extends cn.ykload.flowmix.data.WebSocketMessage {
    <fields>;
    <methods>;
}

# 保护 WebSocket 和同步管理器
-keep class cn.ykload.flowmix.sync.WebSocketManager { *; }
-keep class cn.ykload.flowmix.sync.CloudSyncManager { *; }
-keep class cn.ykload.flowmix.sync.WebSocketMessageListener { *; }
-keep class cn.ykload.flowmix.auth.AuthManager { *; }

# 保护 OkHttp WebSocket 相关类
-keep class okhttp3.WebSocket { *; }
-keep class okhttp3.WebSocketListener { *; }
-keep class okhttp3.Request { *; }
-keep class okhttp3.Response { *; }
-keep class okhttp3.Request$Builder { *; }

# 保护 WebSocket 内部类和匿名类
-keepclassmembers class cn.ykload.flowmix.sync.WebSocketManager {
    *;
}
-keepclassmembers class cn.ykload.flowmix.sync.WebSocketManager$* {
    *;
}

# 保护 Kotlin 协程相关的 WebSocket 操作
-keep class kotlinx.coroutines.Job { *; }
-keep class kotlinx.coroutines.flow.MutableStateFlow { *; }
-keep class kotlinx.coroutines.flow.StateFlow { *; }

# 保护网络状态枚举
-keep enum cn.ykload.flowmix.data.WebSocketState { *; }
-keep enum cn.ykload.flowmix.data.CloudSyncStatus { *; }

# 保护 MessageTypeWrapper 内部类
-keep class cn.ykload.flowmix.sync.WebSocketManager$MessageTypeWrapper { *; }

# ============================================================================
# 系统和第三方库优化配置
# ============================================================================

# SSL/TLS 网络安全 - 仅保护关键类
-keep class javax.net.ssl.SSLSocketFactory { *; }
-keep class javax.net.ssl.HttpsURLConnection { *; }
-dontwarn javax.net.ssl.**
-dontwarn java.security.cert.**

# 网络异常 - 保护关键异常类
-keep class java.io.IOException { *; }
-keep class java.net.SocketException { *; }
-keep class java.net.ConnectException { *; }

# Android系统组件 - 允许混淆但保护关键功能
-dontwarn android.**

# Compose - 精简保护
-dontwarn androidx.compose.**

# ============================================================================
# 性能优化配置
# ============================================================================

# 移除调试日志（Release版本）
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int d(...);
}

# 保留错误和警告日志
-keep class android.util.Log {
    public static int w(...);
    public static int e(...);
    public static int i(...);
}

# ============================================================================
# 最终优化设置
# ============================================================================

# 不混淆异常类名，便于调试
-keep class * extends java.lang.Exception {
    <init>(...);
    public java.lang.String getMessage();
}

# 保护应用入口点
-keep class cn.ykload.flowmix.MainActivity { *; }
-keep class cn.ykload.flowmix.FlowmixApplication { *; }
# End of content from D:\Projects\flowmix\app\proguard-rules.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\f8fbc995fb42104632c0197346495b2a\transformed\foundation-release\proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Keep all the functions created to throw an exception. We don't want these functions to be
# inlined in any way, which R8 will do by default. The whole point of these functions is to
# reduce the amount of code generated at the call site.
-keepclassmembers,allowshrinking,allowobfuscation class androidx.compose.**.* {
    static void throw*Exception(...);
    # For methods returning Nothing
    static java.lang.Void throw*Exception(...);
}


# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\f8fbc995fb42104632c0197346495b2a\transformed\foundation-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\7dd1af14c1d4e888972c1cf6a8e90a43\transformed\animation-core-release\proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Keep all the functions created to throw an exception. We don't want these functions to be
# inlined in any way, which R8 will do by default. The whole point of these functions is to
# reduce the amount of code generated at the call site.
-keepclassmembers,allowshrinking,allowobfuscation class androidx.compose.**.* {
    static void throw*Exception(...);
    static void throw*ExceptionForNullCheck(...);
    # For methods returning Nothing
    static java.lang.Void throw*Exception(...);
    static java.lang.Void throw*ExceptionForNullCheck(...);
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\7dd1af14c1d4e888972c1cf6a8e90a43\transformed\animation-core-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\1386be00d9a7e5ee4ad21ded08c440bd\transformed\animation-release\proguard.txt
# Intentionally empty proguard rules to indicate this library is safe to shrink

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\1386be00d9a7e5ee4ad21ded08c440bd\transformed\animation-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\541ef88f5273b8c27fa00406f560cd66\transformed\foundation-layout-release\proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Keep all the functions created to throw an exception. We don't want these functions to be
# inlined in any way, which R8 will do by default. The whole point of these functions is to
# reduce the amount of code generated at the call site.
-keepclassmembers,allowshrinking,allowobfuscation class androidx.compose.**.* {
    static void throw*Exception(...);
    static void throw*ExceptionForNullCheck(...);
    # For methods returning Nothing
    static java.lang.Void throw*Exception(...);
    static java.lang.Void throw*ExceptionForNullCheck(...);
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\541ef88f5273b8c27fa00406f560cd66\transformed\foundation-layout-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\579e5f4b4789cbcfe415a7a2e1cebfa1\transformed\ui-geometry-release\proguard.txt
# Intentionally empty proguard rules to indicate this library is safe to shrink

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\579e5f4b4789cbcfe415a7a2e1cebfa1\transformed\ui-geometry-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\13b67d1807c70a743f19d134201343fb\transformed\ui-tooling-preview-release\proguard.txt
# Intentionally empty proguard rules to indicate this library is safe to shrink

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\13b67d1807c70a743f19d134201343fb\transformed\ui-tooling-preview-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\b2b52b31ce7c4925c60b4b6406a02271\transformed\ui-text-release\proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Keep all the functions created to throw an exception. We don't want these functions to be
# inlined in any way, which R8 will do by default. The whole point of these functions is to
# reduce the amount of code generated at the call site.
-keepclassmembers,allowshrinking,allowobfuscation class androidx.compose.**.* {
    static void throw*Exception(...);
    static void throw*ExceptionForNullCheck(...);
    # For methods returning Nothing
    static java.lang.Void throw*Exception(...);
    static java.lang.Void throw*ExceptionForNullCheck(...);
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\b2b52b31ce7c4925c60b4b6406a02271\transformed\ui-text-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\a41851883761cbcf6a3a059d99d78c1a\transformed\ui-util-release\proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Keep all the functions created to throw an exception. We don't want these functions to be
# inlined in any way, which R8 will do by default. The whole point of these functions is to
# reduce the amount of code generated at the call site.
-keepclassmembers,allowshrinking,allowobfuscation class androidx.compose.**.* {
    static void throw*Exception(...);
    static void throw*ExceptionForNullCheck(...);
    # For methods returning Nothing
    static java.lang.Void throw*Exception(...);
    static java.lang.Void throw*ExceptionForNullCheck(...);
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\a41851883761cbcf6a3a059d99d78c1a\transformed\ui-util-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\b6763d2378a08d45315bd7079c8bbc10\transformed\ui-unit-release\proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Keep all the functions created to throw an exception. We don't want these functions to be
# inlined in any way, which R8 will do by default. The whole point of these functions is to
# reduce the amount of code generated at the call site.
-keepclassmembers,allowshrinking,allowobfuscation class androidx.compose.**.* {
    static void throw*Exception(...);
    static void throw*ExceptionForNullCheck(...);
    # For methods returning Nothing
    static java.lang.Void throw*Exception(...);
    static java.lang.Void throw*ExceptionForNullCheck(...);
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\b6763d2378a08d45315bd7079c8bbc10\transformed\ui-unit-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\29c4e80b68c78b371e0f1a9fbbe0e894\transformed\ui-graphics-release\proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Keep all the functions created to throw an exception. We don't want these functions to be
# inlined in any way, which R8 will do by default. The whole point of these functions is to
# reduce the amount of code generated at the call site.
-keepclassmembers,allowshrinking,allowobfuscation class androidx.compose.**.* {
    static void throw*Exception(...);
    static void throw*ExceptionForNullCheck(...);
    # For methods returning Nothing
    static java.lang.Void throw*Exception(...);
    static java.lang.Void throw*ExceptionForNullCheck(...);
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\29c4e80b68c78b371e0f1a9fbbe0e894\transformed\ui-graphics-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\92408c5a979bd5b5a46fef3fa24aad18\transformed\graphics-path-1.0.1\proguard.txt
-keepclasseswithmembers class androidx.graphics.path.** {
    native <methods>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\92408c5a979bd5b5a46fef3fa24aad18\transformed\graphics-path-1.0.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\223d83a19ec36f69ccc1b09c90f704ed\transformed\core-1.16.0\proguard.txt
# Never inline methods, but allow shrinking and obfuscation.
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.view.ViewCompat$Api* {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.view.WindowInsetsCompat$*Impl* {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.app.NotificationCompat$*$Api*Impl {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.os.UserHandleCompat$Api*Impl {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.widget.EdgeEffectCompat$Api*Impl {
  <methods>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\223d83a19ec36f69ccc1b09c90f704ed\transformed\core-1.16.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\77927df24898ef2cabaa745d5d82b527\transformed\lifecycle-process-2.9.2\proguard.txt
# this rule is need to work properly when app is compiled with api 28, see b/142778206
-keepclassmembers class * extends androidx.lifecycle.EmptyActivityLifecycleCallbacks { *; }
# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\77927df24898ef2cabaa745d5d82b527\transformed\lifecycle-process-2.9.2\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\f7326da768cb0c7959dd5c663c7bede0\transformed\lifecycle-runtime-compose-release\proguard.txt
# Workaround for https://issuetracker.google.com/issues/346808608
#
# `androidx.lifecycle.compose.LocalLifecycleOwner` will reflectively lookup for
# `androidx.compose.ui.platform.LocalLifecycleOwner` to ensure backward compatibility
# when using Lifecycle 2.8+ with Compose 1.6.
#
# We need to keep the getter if the code using this is included.
#
# We need to suppress `ShrinkerUnresolvedReference` because the `LocalComposition` is in a
# different module.
#
#noinspection ShrinkerUnresolvedReference
-if public class androidx.compose.ui.platform.AndroidCompositionLocals_androidKt {
    public static *** getLocalLifecycleOwner();
}
-keep public class androidx.compose.ui.platform.AndroidCompositionLocals_androidKt {
    public static *** getLocalLifecycleOwner();
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\f7326da768cb0c7959dd5c663c7bede0\transformed\lifecycle-runtime-compose-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\af36734c947ded337103e8b2fb2a14bc\transformed\lifecycle-livedata-core-2.9.2\proguard.txt
# Intentionally empty proguard rules to indicate this library is safe to shrink

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\af36734c947ded337103e8b2fb2a14bc\transformed\lifecycle-livedata-core-2.9.2\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\5870c877b557da0497556c4c5b78f0c5\transformed\lifecycle-viewmodel-savedstate-release\proguard.txt
-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.ViewModel {
    <init>(androidx.lifecycle.SavedStateHandle);
}

-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.AndroidViewModel {
    <init>(android.app.Application,androidx.lifecycle.SavedStateHandle);
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\5870c877b557da0497556c4c5b78f0c5\transformed\lifecycle-viewmodel-savedstate-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\e1611ab9abae75e3a436f6f8340a536a\transformed\shrink-rules\lib\META-INF\proguard\androidx-lifecycle-lifecycle-common-java8.pro
# Intentionally empty proguard rules to indicate this library is safe to shrink

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\e1611ab9abae75e3a436f6f8340a536a\transformed\shrink-rules\lib\META-INF\proguard\androidx-lifecycle-lifecycle-common-java8.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\4de1744565ebe041cb26ca7b8010109d\transformed\lifecycle-viewmodel-release\proguard.txt
-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.ViewModel {
    <init>();
}

-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.AndroidViewModel {
    <init>(android.app.Application);
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\4de1744565ebe041cb26ca7b8010109d\transformed\lifecycle-viewmodel-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\e6203dfae908327a71d3e7b1205255c3\transformed\savedstate-ktx-1.3.1\proguard.txt
# Intentionally empty proguard rules to indicate this library is safe to shrink

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\e6203dfae908327a71d3e7b1205255c3\transformed\savedstate-ktx-1.3.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\0005607a0121ef3d1fa77a40990e09f9\transformed\savedstate-release\proguard.txt
# Copyright (C) 2019 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

-keepclassmembers,allowobfuscation class * implements androidx.savedstate.SavedStateRegistry$AutoRecreated {
    <init>();
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\0005607a0121ef3d1fa77a40990e09f9\transformed\savedstate-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\145dc04648c370fc5888bbd6c784dc2c\transformed\lifecycle-runtime-release\proguard.txt
-keepattributes AnnotationDefault,
                RuntimeVisibleAnnotations,
                RuntimeVisibleParameterAnnotations,
                RuntimeVisibleTypeAnnotations

-keepclassmembers enum androidx.lifecycle.Lifecycle$Event {
    <fields>;
}

-keep class * implements androidx.lifecycle.GeneratedAdapter {
    <init>(...);
}

-keepclassmembers class ** {
    @androidx.lifecycle.OnLifecycleEvent *;
}

# The deprecated `android.app.Fragment` creates `Fragment` instances using reflection.
# See: b/338958225, b/341537875
-keepclasseswithmembers,allowobfuscation public class androidx.lifecycle.ReportFragment {
    public <init>();
}

# this rule is need to work properly when app is compiled with api 28, see b/142778206
# Also this rule prevents registerIn from being inlined.
-keepclassmembers class androidx.lifecycle.ReportFragment$LifecycleCallbacks { *; }

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\145dc04648c370fc5888bbd6c784dc2c\transformed\lifecycle-runtime-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\fe8d75863dc46170a2cbdbd222802706\transformed\lifecycle-viewmodel-ktx-2.9.2\proguard.txt
# Intentionally empty proguard rules to indicate this library is safe to shrink

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\fe8d75863dc46170a2cbdbd222802706\transformed\lifecycle-viewmodel-ktx-2.9.2\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\e8db837ceb7844d83f9738025fcb93bc\transformed\lifecycle-viewmodel-compose-release\proguard.txt
# Intentionally empty proguard rules to indicate this library is safe to shrink

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\e8db837ceb7844d83f9738025fcb93bc\transformed\lifecycle-viewmodel-compose-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\359440fcf7f5a82864f0f58a2c2787a7\transformed\ui-release\proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# We supply these as stubs and are able to link to them at runtime
# because they are hidden public classes in Android. We don't want
# R8 to complain about them not being there during optimization.
-dontwarn android.view.RenderNode
-dontwarn android.view.DisplayListCanvas
-dontwarn android.view.HardwareCanvas

-keepclassmembers class androidx.compose.ui.platform.ViewLayerContainer {
    protected void dispatchGetDisplayList();
}

-keepclassmembers class androidx.compose.ui.platform.AndroidComposeView {
    android.view.View findViewByAccessibilityIdTraversal(int);
}

# Users can create Modifier.Node instances that implement multiple Modifier.Node interfaces,
# so we cannot tell whether two modifier.node instances are of the same type without using
# reflection to determine the class type. See b/265188224 for more context.
-keep,allowshrinking class * extends androidx.compose.ui.node.ModifierNodeElement

# Keep all the functions created to throw an exception. We don't want these functions to be
# inlined in any way, which R8 will do by default. The whole point of these functions is to
# reduce the amount of code generated at the call site.
-keepclassmembers,allowshrinking,allowobfuscation class androidx.compose.**.* {
    static void throw*Exception(...);
    static void throw*ExceptionForNullCheck(...);
    # For methods returning Nothing
    static java.lang.Void throw*Exception(...);
    static java.lang.Void throw*ExceptionForNullCheck(...);
    # For functions generating error messages
    static java.lang.String exceptionMessage*(...);
    java.lang.String exceptionMessage*(...);
}

# When pointer input modifier nodes are added dynamically and have the same keys (common when
# developers `Unit` for their keys), we need a way to differentiate them and using a
# functional interface and comparing classes allows us to do that.
-keepnames class androidx.compose.ui.input.pointer.PointerInputEventHandler {
    *;
}


# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\359440fcf7f5a82864f0f58a2c2787a7\transformed\ui-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\93c707ee6291c583fe5bbea7f4ec012c\transformed\core-ktx-1.16.0\proguard.txt
# Intentionally empty proguard rules to indicate this library is safe to shrink

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\93c707ee6291c583fe5bbea7f4ec012c\transformed\core-ktx-1.16.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\90f2c98f9e4700dc0461aa836e2e49c9\transformed\runtime-saveable-release\proguard.txt
# Intentionally empty proguard rules to indicate this library is safe to shrink

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\90f2c98f9e4700dc0461aa836e2e49c9\transformed\runtime-saveable-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\30e53ffe2fb6d65984d12c78b7f39145\transformed\runtime-release\proguard.txt
-assumenosideeffects public class androidx.compose.runtime.ComposerKt {
    void sourceInformation(androidx.compose.runtime.Composer,java.lang.String);
    void sourceInformationMarkerStart(androidx.compose.runtime.Composer,int,java.lang.String);
    void sourceInformationMarkerEnd(androidx.compose.runtime.Composer);
}

# Composer's class initializer doesn't do anything but create an EMPTY object. Marking the
# initializers as having no side effects can help encourage shrinkers to merge/devirtualize Composer
# with ComposerImpl.
-assumenosideeffects public class androidx.compose.runtime.Composer {
    void <clinit>();
}
-assumenosideeffects public class androidx.compose.runtime.ComposerImpl {
    void <clinit>();
}

# Keep all the functions created to throw an exception. We don't want these functions to be
# inlined in any way, which R8 will do by default. The whole point of these functions is to
# reduce the amount of code generated at the call site.
-keepclassmembers,allowshrinking,allowobfuscation class androidx.compose.runtime.** {
    # java.lang.Void == methods that return Nothing
    static void throw*Exception(...);
    static void throw*ExceptionForNullCheck(...);
    static java.lang.Void throw*Exception(...);
    static java.lang.Void throw*ExceptionForNullCheck(...);

    # For functions generating error messages
    static java.lang.String exceptionMessage*(...);
    java.lang.String exceptionMessage*(...);

    static void compose*RuntimeError(...);
    static java.lang.Void compose*RuntimeError(...);
}


# Intentionally empty proguard rules to indicate this library is safe to shrink

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\30e53ffe2fb6d65984d12c78b7f39145\transformed\runtime-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\e72e0bba910be40df3c3f80fb3e94468\transformed\core-viewtree-1.0.0\proguard.txt
# Intentionally empty proguard rules to indicate this library is safe to shrink

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\e72e0bba910be40df3c3f80fb3e94468\transformed\core-viewtree-1.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\d2381905529dc57d28f7672bcb51cfc4\transformed\shrink-rules\lib\META-INF\com.android.tools\r8\coroutines.pro
# When editing this file, update the following files as well:
# - META-INF/proguard/coroutines.pro
# - META-INF/com.android.tools/proguard/coroutines.pro

# Most of volatile fields are updated with AFU and should not be mangled
-keepclassmembers class kotlinx.coroutines.** {
    volatile <fields>;
}

# Same story for the standard library's SafeContinuation that also uses AtomicReferenceFieldUpdater
-keepclassmembers class kotlin.coroutines.SafeContinuation {
    volatile <fields>;
}

# These classes are only required by kotlinx.coroutines.debug.AgentPremain, which is only loaded when
# kotlinx-coroutines-core is used as a Java agent, so these are not needed in contexts where ProGuard is used.
-dontwarn java.lang.instrument.ClassFileTransformer
-dontwarn sun.misc.SignalHandler
-dontwarn java.lang.instrument.Instrumentation
-dontwarn sun.misc.Signal

# Only used in `kotlinx.coroutines.internal.ExceptionsConstructor`.
# The case when it is not available is hidden in a `try`-`catch`, as well as a check for Android.
-dontwarn java.lang.ClassValue

# An annotation used for build tooling, won't be directly accessed.
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement
# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\d2381905529dc57d28f7672bcb51cfc4\transformed\shrink-rules\lib\META-INF\com.android.tools\r8\coroutines.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\74afbd75fa5dc8d222791f360ee51475\transformed\shrink-rules\lib\META-INF\com.android.tools\r8-from-1.6.0\coroutines.pro
# Allow R8 to optimize away the FastServiceLoader.
# Together with ServiceLoader optimization in R8
# this results in direct instantiation when loading Dispatchers.Main
-assumenosideeffects class kotlinx.coroutines.internal.MainDispatcherLoader {
    boolean FAST_SERVICE_LOADER_ENABLED return false;
}

-assumenosideeffects class kotlinx.coroutines.internal.FastServiceLoaderKt {
    boolean ANDROID_DETECTED return true;
}

# Disable support for "Missing Main Dispatcher", since we always have Android main dispatcher
-assumenosideeffects class kotlinx.coroutines.internal.MainDispatchersKt {
    boolean SUPPORT_MISSING return false;
}

# Statically turn off all debugging facilities and assertions
-assumenosideeffects class kotlinx.coroutines.DebugKt {
    boolean getASSERTIONS_ENABLED() return false;
    boolean getDEBUG() return false;
    boolean getRECOVER_STACK_TRACES() return false;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\74afbd75fa5dc8d222791f360ee51475\transformed\shrink-rules\lib\META-INF\com.android.tools\r8-from-1.6.0\coroutines.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\699d8cc78fba3b42c7f1dbad9a421ee4\transformed\startup-runtime-1.1.1\proguard.txt
# It's important that we preserve initializer names, given they are used in the AndroidManifest.xml.
-keepnames class * extends androidx.startup.Initializer

# These Proguard rules ensures that ComponentInitializers are are neither shrunk nor obfuscated,
# and are a part of the primary dex file. This is because they are discovered and instantiated
# during application startup.
-keep class * extends androidx.startup.Initializer {
    # Keep the public no-argument constructor while allowing other methods to be optimized.
    <init>();
}

-assumenosideeffects class androidx.startup.StartupLogger { public static <methods>; }

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\699d8cc78fba3b42c7f1dbad9a421ee4\transformed\startup-runtime-1.1.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\767263a25e5096722de245dfbcd090de\transformed\versionedparcelable-1.1.1\proguard.txt
-keep class * implements androidx.versionedparcelable.VersionedParcelable
-keep public class android.support.**Parcelizer { *; }
-keep public class androidx.**Parcelizer { *; }
-keep public class androidx.versionedparcelable.ParcelImpl

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\767263a25e5096722de245dfbcd090de\transformed\versionedparcelable-1.1.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\3696741bdf8007d941b9c88118566c58\transformed\shrink-rules\lib\META-INF\proguard\androidx-collection-collection-ktx.pro
# Intentionally empty proguard rules to indicate this library is safe to shrink

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\3696741bdf8007d941b9c88118566c58\transformed\shrink-rules\lib\META-INF\proguard\androidx-collection-collection-ktx.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\8075025dbcfef910cc6a98f747e969ca\transformed\shrink-rules\lib\META-INF\proguard\androidx-annotations.pro
-keep,allowobfuscation @interface androidx.annotation.Keep
-keep @androidx.annotation.Keep class * {*;}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <init>(...);
}

-keepclassmembers,allowobfuscation class * {
  @androidx.annotation.DoNotInline <methods>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\8075025dbcfef910cc6a98f747e969ca\transformed\shrink-rules\lib\META-INF\proguard\androidx-annotations.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\4bc69a81a15017bc843c3a6f98908a50\transformed\shrink-rules\lib\META-INF\proguard\retrofit2.pro
# Retrofit does reflection on generic parameters. InnerClasses is required to use Signature and
# EnclosingMethod is required to use InnerClasses.
-keepattributes Signature, InnerClasses, EnclosingMethod

# Retrofit does reflection on method and parameter annotations.
-keepattributes RuntimeVisibleAnnotations, RuntimeVisibleParameterAnnotations

# Retain service method parameters when optimizing.
-keepclassmembers,allowshrinking,allowobfuscation interface * {
    @retrofit2.http.* <methods>;
}

# Ignore annotation used for build tooling.
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement

# Ignore JSR 305 annotations for embedding nullability information.
-dontwarn javax.annotation.**

# Guarded by a NoClassDefFoundError try/catch and only used when on the classpath.
-dontwarn kotlin.Unit

# Top-level functions that can only be used by Kotlin.
-dontwarn retrofit2.KotlinExtensions
-dontwarn retrofit2.KotlinExtensions$*

# With R8 full mode, it sees no subtypes of Retrofit interfaces since they are created with a Proxy
# and replaces all potential values with null. Explicitly keeping the interfaces prevents this.
-if interface * { @retrofit2.http.* <methods>; }
-keep,allowobfuscation interface <1>

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\4bc69a81a15017bc843c3a6f98908a50\transformed\shrink-rules\lib\META-INF\proguard\retrofit2.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\c3060f93f89d78f6d7486144cf328f81\transformed\shrink-rules\lib\META-INF\proguard\okhttp3.pro
# JSR 305 annotations are for embedding nullability information.
-dontwarn javax.annotation.**

# A resource is loaded with a relative path so the package of this class must be preserved.
-keepnames class okhttp3.internal.publicsuffix.PublicSuffixDatabase

# Animal Sniffer compileOnly dependency to ensure APIs are compatible with older versions of Java.
-dontwarn org.codehaus.mojo.animal_sniffer.*

# OkHttp platform used only on JVM and when Conscrypt and other security providers are available.
-dontwarn okhttp3.internal.platform.**
-dontwarn org.conscrypt.**
-dontwarn org.bouncycastle.**
-dontwarn org.openjsse.**

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\c3060f93f89d78f6d7486144cf328f81\transformed\shrink-rules\lib\META-INF\proguard\okhttp3.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\486cb7926c123b5d72e56523b503e53a\transformed\shrink-rules\lib\META-INF\com.android.tools\r8\kotlinx-serialization-common.pro
# Keep `Companion` object fields of serializable classes.
# This avoids serializer lookup through `getDeclaredClasses` as done for named companion objects.
-if @kotlinx.serialization.Serializable class **
-keepclassmembers class <1> {
    static <1>$Companion Companion;
}

# Keep `serializer()` on companion objects (both default and named) of serializable classes.
-if @kotlinx.serialization.Serializable class ** {
    static **$* *;
}
-keepclassmembers class <2>$<3> {
    kotlinx.serialization.KSerializer serializer(...);
}

# Keep `INSTANCE.serializer()` of serializable objects.
-if @kotlinx.serialization.Serializable class ** {
    public static ** INSTANCE;
}
-keepclassmembers class <1> {
    public static <1> INSTANCE;
    kotlinx.serialization.KSerializer serializer(...);
}

# @Serializable and @Polymorphic are used at runtime for polymorphic serialization.
-keepattributes RuntimeVisibleAnnotations,AnnotationDefault

# Don't print notes about potential mistakes or omissions in the configuration for kotlinx-serialization classes
# See also https://github.com/Kotlin/kotlinx.serialization/issues/1900
-dontnote kotlinx.serialization.**

# Serialization core uses `java.lang.ClassValue` for caching inside these specified classes.
# If there is no `java.lang.ClassValue` (for example, in Android), then R8/ProGuard will print a warning.
# However, since in this case they will not be used, we can disable these warnings
-dontwarn kotlinx.serialization.internal.ClassValueReferences

# disable optimisation for descriptor field because in some versions of ProGuard, optimization generates incorrect bytecode that causes a verification error
# see https://github.com/Kotlin/kotlinx.serialization/issues/2719
-keepclassmembers public class **$$serializer {
    private ** descriptor;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\486cb7926c123b5d72e56523b503e53a\transformed\shrink-rules\lib\META-INF\com.android.tools\r8\kotlinx-serialization-common.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\486cb7926c123b5d72e56523b503e53a\transformed\shrink-rules\lib\META-INF\com.android.tools\r8\kotlinx-serialization-r8.pro
# Rule to save runtime annotations on serializable class.
# If the R8 full mode is used, annotations are removed from classes-files.
#
# For the annotation serializer, it is necessary to read the `Serializable` annotation inside the serializer<T>() function - if it is present,
# then `SealedClassSerializer` is used, if absent, then `PolymorphicSerializer'.
#
# When using R8 full mode, all interfaces will be serialized using `PolymorphicSerializer`.
#
# see https://github.com/Kotlin/kotlinx.serialization/issues/2050

 -if @kotlinx.serialization.Serializable class **
 -keep, allowshrinking, allowoptimization, allowobfuscation, allowaccessmodification class <1>

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\486cb7926c123b5d72e56523b503e53a\transformed\shrink-rules\lib\META-INF\com.android.tools\r8\kotlinx-serialization-r8.pro
# The proguard configuration file for the following section is <unknown>

# End of content from <unknown>