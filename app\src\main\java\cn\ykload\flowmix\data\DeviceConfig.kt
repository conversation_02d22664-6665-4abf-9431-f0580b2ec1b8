package cn.ykload.flowmix.data

import cn.ykload.flowmix.network.NetworkManager
import com.google.gson.annotations.SerializedName

/**
 * 设备配置数据类
 * 存储每个音频设备对应的AutoEq和频响配置
 */
data class DeviceConfig(
    @SerializedName("deviceId")
    val deviceId: String,
    
    @SerializedName("deviceName")
    val deviceName: String,
    
    @SerializedName("deviceType")
    val deviceType: String,
    
    @SerializedName("autoEqConfig")
    val autoEqConfig: AutoEqConfig? = null,
    
    @SerializedName("frequencyResponseConfig")
    val frequencyResponseConfig: FrequencyResponseConfig? = null,
    
    @SerializedName("lastUpdated")
    val lastUpdated: Long = System.currentTimeMillis()
) {
    /**
     * 检查配置是否为空
     */
    fun isEmpty(): Boolean {
        return autoEqConfig == null && frequencyResponseConfig == null
    }
    
    /**
     * 检查配置是否完整
     */
    fun isComplete(): Boolean {
        return autoEqConfig != null && frequencyResponseConfig != null
    }
}

/**
 * AutoEq配置
 */
data class AutoEqConfig(
    @SerializedName("name")
    val name: String,
    
    @SerializedName("bands")
    val bands: List<EqBandConfig>,
    
    @SerializedName("isLoudnessCompensationEnabled")
    val isLoudnessCompensationEnabled: Boolean = false,
    
    @SerializedName("globalGain")
    val globalGain: Float = 0f
) {
    /**
     * 转换为AutoEqData
     */
    fun toAutoEqData(): AutoEqData {
        val eqBands = bands.map { band ->
            EqBand(band.frequency, band.gain)
        }
        return AutoEqData(eqBands, name)
    }
    
    companion object {
        /**
         * 从AutoEqData创建配置
         */
        fun fromAutoEqData(
            autoEqData: AutoEqData,
            isLoudnessCompensationEnabled: Boolean = false,
            globalGain: Float = 0f
        ): AutoEqConfig {
            val bands = autoEqData.bands.map { band ->
                EqBandConfig(band.frequency, band.gain)
            }
            return AutoEqConfig(
                name = autoEqData.name,
                bands = bands,
                isLoudnessCompensationEnabled = isLoudnessCompensationEnabled,
                globalGain = globalGain
            )
        }
    }
}

/**
 * EQ频段配置
 */
data class EqBandConfig(
    @SerializedName("frequency")
    val frequency: Float,
    
    @SerializedName("gain")
    val gain: Float
)

/**
 * 频响配置
 * 注意：目标曲线数据已从FlowSync同步范围中移除，不再参与设备间同步
 */
data class FrequencyResponseConfig(
    @SerializedName("dataSource")
    val dataSource: String? = null,

    @SerializedName("brand")
    val brand: String? = null,

    @SerializedName("headphone")
    val headphone: String? = null,

    @SerializedName("measurementCondition")
    val measurementCondition: String? = null
) {
    /**
     * 检查配置是否完整
     * 注意：不再检查目标曲线，因为它不参与FlowSync同步
     */
    fun isComplete(): Boolean {
        return dataSource != null && brand != null && headphone != null &&
               measurementCondition != null
    }

    /**
     * 获取显示名称
     */
    fun getDisplayName(): String {
        return if (isComplete()) {
            "$brand $headphone"
        } else {
            "未配置"
        }
    }
}

/**
 * 设备配置集合
 * 用于存储所有设备的配置
 * 使用时间戳而不是版本号进行同步控制
 */
data class DeviceConfigCollection(
    @SerializedName("configs")
    val configs: Map<String, DeviceConfig> = emptyMap(),

    @SerializedName("lastUpdated")
    val lastUpdated: Long = System.currentTimeMillis()
) {
    /**
     * 获取指定设备的配置
     */
    fun getConfig(deviceId: String): DeviceConfig? {
        return configs[deviceId]
    }
    
    /**
     * 添加或更新设备配置
     */
    fun withConfig(deviceConfig: DeviceConfig): DeviceConfigCollection {
        val updatedConfigs = configs.toMutableMap()
        updatedConfigs[deviceConfig.deviceId] = deviceConfig
        return copy(
            configs = updatedConfigs,
            lastUpdated = System.currentTimeMillis()
        )
    }
    
    /**
     * 移除设备配置
     */
    fun withoutConfig(deviceId: String): DeviceConfigCollection {
        val updatedConfigs = configs.toMutableMap()
        updatedConfigs.remove(deviceId)
        return copy(
            configs = updatedConfigs,
            lastUpdated = System.currentTimeMillis()
        )
    }
    
    /**
     * 获取所有已配置的设备
     */
    fun getConfiguredDevices(): List<DeviceConfig> {
        return configs.values.filter { !it.isEmpty() }
    }
    
    /**
     * 转换为JSON字符串
     */
    fun toJson(): String {
        return NetworkManager.getGson().toJson(this)
    }

    companion object {
        /**
         * 从JSON字符串创建
         */
        fun fromJson(json: String): DeviceConfigCollection? {
            return try {
                NetworkManager.getGson().fromJson(json, DeviceConfigCollection::class.java)
            } catch (e: Exception) {
                null
            }
        }
        
        /**
         * 创建空的配置集合
         */
        fun empty(): DeviceConfigCollection {
            return DeviceConfigCollection()
        }
    }
}
