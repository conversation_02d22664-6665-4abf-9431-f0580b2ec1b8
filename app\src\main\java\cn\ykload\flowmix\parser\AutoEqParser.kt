package cn.ykload.flowmix.parser

import cn.ykload.flowmix.data.AutoEqData
import cn.ykload.flowmix.data.EqBand
import cn.ykload.flowmix.utils.Constants
import java.io.InputStream

/**
 * AutoEq文件解析器
 * 支持解析GraphicEQ格式的txt文件
 */
class AutoEqParser {
    
    companion object {
        private const val TAG = "AutoEqParser"
    }
    
    /**
     * 从InputStream解析AutoEq数据
     * @param inputStream 输入流
     * @param fileName 文件名（用作配置名称）
     * @return AutoEqData对象，解析失败时返回null
     */
    fun parseFromInputStream(inputStream: InputStream, fileName: String = "AutoEq"): AutoEqData? {
        return try {
            val content = inputStream.bufferedReader().use { it.readText() }
            parseFromString(content, fileName)
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
    
    /**
     * 从字符串解析AutoEq数据
     * @param content 文件内容
     * @param name 配置名称
     * @return AutoEqData对象，解析失败时返回null
     */
    fun parseFromString(content: String, name: String = "AutoEq"): AutoEqData? {
        return try {
            val lines = content.lines()
            
            // 查找GraphicEQ行
            val graphicEqLine = lines.find { line ->
                line.trim().startsWith(Constants.AutoEqFormat.GRAPHIC_EQ_PREFIX, ignoreCase = true)
            } ?: return null

            // 提取EQ数据部分
            val eqDataPart = graphicEqLine.substring(Constants.AutoEqFormat.GRAPHIC_EQ_PREFIX.length).trim()
            
            // 解析频段数据
            val bands = parseEqBands(eqDataPart)
            
            if (bands.isEmpty()) {
                return null
            }
            
            AutoEqData(bands = bands, name = name)
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
    
    /**
     * 解析EQ频段数据
     * 格式: "20 0; 21 0; 22 0; ..."
     */
    private fun parseEqBands(eqDataPart: String): List<EqBand> {
        val bands = mutableListOf<EqBand>()
        
        try {
            val bandStrings = eqDataPart.split(Constants.AutoEqFormat.BAND_SEPARATOR)

            for (bandString in bandStrings) {
                val trimmed = bandString.trim()
                if (trimmed.isEmpty()) continue

                val parts = trimmed.split(Constants.AutoEqFormat.FREQ_GAIN_SEPARATOR)
                if (parts.size >= 2) {
                    val frequency = parts[0].trim().toFloatOrNull()
                    val gain = parts[1].trim().toFloatOrNull()

                    if (frequency != null && gain != null &&
                        frequency >= Constants.MIN_FREQUENCY_HZ &&
                        frequency <= Constants.MAX_FREQUENCY_HZ &&
                        gain >= Constants.MIN_GAIN_DB &&
                        gain <= Constants.MAX_GAIN_DB) {
                        bands.add(EqBand(frequency, gain))
                    }
                }
            }

            // 检查频段数量限制
            if (bands.size > Constants.MAX_EQ_BANDS) {
                return bands.take(Constants.MAX_EQ_BANDS)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        
        return bands.sortedBy { it.frequency }
    }
    
    /**
     * 验证AutoEq文件格式
     * @param content 文件内容
     * @return 是否为有效的AutoEq格式
     */
    fun isValidAutoEqFormat(content: String): Boolean {
        return try {
            val lines = content.lines()
            lines.any { line ->
                line.trim().startsWith(Constants.AutoEqFormat.GRAPHIC_EQ_PREFIX, ignoreCase = true)
            }
        } catch (e: Exception) {
            false
        }
    }
}
