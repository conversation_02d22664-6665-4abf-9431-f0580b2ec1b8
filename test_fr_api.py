#!/usr/bin/env python3
"""
测试频响API的可用性
"""

import requests
import json
import sys

def test_api_endpoint(url, description):
    """测试API端点"""
    print(f"\n测试 {description}...")
    print(f"URL: {url}")
    
    try:
        response = requests.get(url, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"响应类型: {type(data)}")
                if isinstance(data, dict):
                    print(f"响应键: {list(data.keys())}")
                    if 'success' in data:
                        print(f"成功状态: {data.get('success')}")
                    if 'message' in data:
                        print(f"消息: {data.get('message')}")
                    if 'data' in data and data['data']:
                        if isinstance(data['data'], list):
                            print(f"数据项数量: {len(data['data'])}")
                            if len(data['data']) > 0:
                                print(f"第一项: {data['data'][0]}")
                        else:
                            print(f"数据: {data['data']}")
                else:
                    print(f"响应内容: {data}")
                print("✅ 测试通过")
                return True
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"响应内容: {response.text[:200]}...")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text[:200]}...")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("FlowMix 频响API测试")
    print("=" * 50)
    
    base_url = "https://fr-api.ykload.com"
    
    # 测试端点列表
    test_cases = [
        (f"{base_url}/api/sources", "获取数据源列表"),
        (f"{base_url}/api/sources/realab/brands", "获取Realab品牌列表"),
        (f"{base_url}/api/targets", "获取目标曲线列表"),
    ]
    
    results = []
    
    for url, description in test_cases:
        success = test_api_endpoint(url, description)
        results.append((description, success))
    
    # 总结
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    
    passed = 0
    for description, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{description}: {status}")
        if success:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有API测试通过！")
        return 0
    else:
        print("⚠️  部分API测试失败，请检查网络连接或API服务状态")
        return 1

if __name__ == "__main__":
    sys.exit(main())
