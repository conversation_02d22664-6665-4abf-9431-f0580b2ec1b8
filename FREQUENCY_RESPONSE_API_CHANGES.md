# 频响页面数据源选择功能更新

## 概述

本次更新为频响页面添加了数据源选择功能，并更新了相关的API接口以支持多数据源架构。

## 主要更改

### 1. 数据模型更新

#### 新增数据源模型
```kotlin
data class DataSource(
    val name: String,
    val displayName: String,
    val description: String
)
```

#### 更新耳机模型
```kotlin
data class Headphone(
    val fileName: String,
    val originalName: String,
    val lastUpdated: String,
    val sourceName: String? = null  // 新增字段
)
```

#### 更新频响数据模型
```kotlin
data class HeadphoneFrequencyData(
    val sourceName: String,  // 新增字段
    val brandName: String,
    val headphoneName: String,
    val lastUpdated: String,
    val frequencyData: Map<String, MeasurementCondition>
)
```

### 2. API接口更新

#### 新增数据源接口
```kotlin
// 获取所有数据源
@GET("api/sources")
suspend fun getDataSources(): Response<DataSourcesResponse>
```

#### 更新现有接口
```kotlin
// 获取指定数据源的所有品牌
@GET("api/sources/{sourceName}/brands")
suspend fun getBrands(@Path("sourceName") sourceName: String): Response<BrandsResponse>

// 获取指定数据源和品牌下的所有耳机
@GET("api/sources/{sourceName}/brands/{brandName}/headphones")
suspend fun getHeadphones(
    @Path("sourceName") sourceName: String,
    @Path("brandName") brandName: String
): Response<HeadphonesResponse>

// 获取指定数据源、品牌和耳机的频响数据
@GET("api/sources/{sourceName}/brands/{brandName}/headphones/{headphoneName}")
suspend fun getFrequencyData(
    @Path("sourceName") sourceName: String,
    @Path("brandName") brandName: String,
    @Path("headphoneName") headphoneName: String
): Response<FrequencyDataResponse>
```

### 3. ViewModel更新

#### 新增状态字段
```kotlin
data class FrequencyResponseUiState(
    val dataSources: List<DataSource> = emptyList(),
    val selectedDataSource: DataSource? = null,
    // ... 其他现有字段
)
```

#### 新增方法
```kotlin
// 加载数据源
fun loadDataSources()

// 选择数据源
fun selectDataSource(dataSource: DataSource)

// 更新的品牌加载方法
fun loadBrands(sourceName: String)
```

### 4. UI组件更新

#### 新增数据源选择下拉框
- 显示数据源的显示名称和描述
- 支持动画过渡效果
- 与现有的品牌、耳机选择保持一致的设计风格

#### 更新设备选择卡片
- 增加数据源选择作为第一步
- 调整卡片高度以适应新的选择层级
- 更新动画逻辑以支持四级选择（数据源 → 品牌 → 耳机 → 测量条件）

## API响应示例

### 获取数据源列表
```json
{
  "success": true,
  "data": [
    {
      "name": "realab",
      "displayName": "Realab",
      "description": "Realab耳机频响数据源"
    },
    {
      "name": "huihifi",
      "displayName": "HuiHiFi",
      "description": "HuiHiFi耳机频响数据源"
    }
  ],
  "count": 2,
  "message": "获取数据源列表成功"
}
```

### 获取品牌列表
```json
{
  "success": true,
  "data": ["Apple_苹果", "Sony_索尼", "Sennheiser"],
  "count": 3,
  "sourceName": "realab",
  "message": "获取数据源realab的品牌列表成功"
}
```

### 获取耳机列表
```json
{
  "success": true,
  "data": [
    {
      "fileName": "Apple_苹果_AirPods_Max",
      "originalName": "Apple/苹果 AirPods Max",
      "lastUpdated": "2024-01-15T10:30:00.000Z",
      "sourceName": "realab"
    }
  ],
  "count": 1,
  "sourceName": "realab",
  "brandName": "Apple_苹果",
  "message": "获取数据源realab品牌Apple_苹果的耳机列表成功"
}
```

## 用户体验改进

1. **渐进式选择**: 用户首先选择数据源，然后依次选择品牌、耳机和测量条件
2. **动画过渡**: 每个选择步骤都有平滑的动画效果
3. **清晰的层级**: 通过卡片高度变化和动画，清楚地展示选择进度
4. **信息丰富**: 数据源选择显示描述信息，帮助用户理解不同数据源的特点

## 兼容性

- 保持了现有的频响图表组件不变
- 保持了现有的测量条件选择逻辑
- 新增的字段都是可选的，确保向后兼容性

## 测试

已添加单元测试覆盖：
- 数据源模型创建和验证
- 更新后的耳机模型
- API响应结构验证
- 测量条件有效性检查
