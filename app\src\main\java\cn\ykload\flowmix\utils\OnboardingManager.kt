package cn.ykload.flowmix.utils

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import cn.ykload.flowmix.permission.PermissionManager

/**
 * 欢迎页管理器
 * 负责管理欢迎页的显示逻辑和状态持久化
 */
class OnboardingManager(private val context: Context) {

    companion object {
        private const val TAG = "OnboardingManager"
        private const val PREFS_NAME = "flowmix_app_settings"
        private const val KEY_ONBOARDING_COMPLETED = "onboarding_completed"
        private const val KEY_LAST_PERMISSION_CHECK = "last_permission_check"
    }

    private val sharedPreferences: SharedPreferences = 
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

    /**
     * 检查是否应该显示欢迎页
     * @param permissionManager 权限管理器
     * @return true 如果应该显示欢迎页
     */
    fun shouldShowOnboarding(permissionManager: PermissionManager): Boolean {
        val isCompleted = isOnboardingCompleted()
        
        // 如果从未完成过欢迎页，则显示
        if (!isCompleted) {
            Log.d(TAG, "初次启动，显示欢迎页")
            return true
        }

        // 如果已完成欢迎页，检查关键权限是否仍然有效
        val hasRequiredPermissions = permissionManager.hasAudioPermission()
        if (!hasRequiredPermissions) {
            Log.d(TAG, "关键权限缺失，显示欢迎页")
            return true
        }

        Log.d(TAG, "权限充足且已完成欢迎页，跳过欢迎页")
        return false
    }

    /**
     * 检查欢迎页是否已完成
     */
    fun isOnboardingCompleted(): Boolean {
        return sharedPreferences.getBoolean(KEY_ONBOARDING_COMPLETED, false)
    }

    /**
     * 标记欢迎页已完成
     */
    fun markOnboardingCompleted() {
        sharedPreferences.edit()
            .putBoolean(KEY_ONBOARDING_COMPLETED, true)
            .putLong(KEY_LAST_PERMISSION_CHECK, System.currentTimeMillis())
            .apply()
        Log.d(TAG, "已标记欢迎页完成")
    }

    /**
     * 重置欢迎页状态
     * 用于设置页面或权限丢失时重新显示欢迎页
     */
    fun resetOnboardingStatus() {
        sharedPreferences.edit()
            .putBoolean(KEY_ONBOARDING_COMPLETED, false)
            .remove(KEY_LAST_PERMISSION_CHECK)
            .apply()
        Log.d(TAG, "已重置欢迎页状态")
    }

    /**
     * 获取上次权限检查时间
     */
    fun getLastPermissionCheckTime(): Long {
        return sharedPreferences.getLong(KEY_LAST_PERMISSION_CHECK, 0L)
    }

    /**
     * 检查是否需要重新验证权限
     * 如果距离上次检查超过一定时间，可能需要重新检查权限
     */
    fun shouldRecheckPermissions(): Boolean {
        val lastCheck = getLastPermissionCheckTime()
        val now = System.currentTimeMillis()
        val dayInMillis = 24 * 60 * 60 * 1000L // 24小时
        
        return (now - lastCheck) > dayInMillis
    }

    /**
     * 更新权限检查时间
     */
    fun updatePermissionCheckTime() {
        sharedPreferences.edit()
            .putLong(KEY_LAST_PERMISSION_CHECK, System.currentTimeMillis())
            .apply()
    }
}
