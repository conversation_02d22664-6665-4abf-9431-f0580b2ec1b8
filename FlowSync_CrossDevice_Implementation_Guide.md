# FlowSync 跨设备同步功能实现指南

## 概述

FlowSync 跨设备同步功能已完成设计和实现，包括完整的前端架构和详细的后端API规范。本功能允许用户在多个设备间无缝同步音频设备配置，提供一致的音频体验。

## 功能特性

### 1. 用户认证系统
- **6位数字登录码认证**：用户通过QQ Bot获取登录码
- **长期有效的authToken**：登录成功后获得长期有效的认证令牌
- **自动登录**：App启动时自动使用存储的authToken登录
- **安全存储**：authToken安全存储在本地，支持过期检测

### 2. 实时数据同步
- **WebSocket长连接**：与后端建立持久化连接
- **实时双向通信**：支持配置的实时推送和接收
- **自动重连机制**：网络异常时自动重连
- **心跳保持**：定期发送心跳包维持连接

### 3. 智能配置管理
- **云端优先策略**：优先使用云端最新配置
- **版本控制**：支持配置版本管理和冲突检测
- **增量同步**：只同步变更的配置数据
- **离线支持**：网络异常时仍可使用本地配置

## 技术架构

### 前端架构

#### 核心组件

1. **AuthManager** - 认证管理器
   - 管理用户登录状态和authToken
   - 提供登录、自动登录、登出功能
   - 处理token过期和刷新

2. **WebSocketManager** - WebSocket连接管理器
   - 管理与后端的WebSocket连接
   - 处理消息的发送和接收
   - 实现自动重连和心跳机制

3. **CloudSyncManager** - 云端同步管理器
   - 协调认证和WebSocket连接
   - 管理本地和云端配置的同步
   - 处理配置冲突和版本控制

4. **LoginScreen & LoginViewModel** - 登录界面
   - 提供用户友好的登录界面
   - 处理登录码输入和验证
   - 显示登录状态和错误信息

#### 数据流程

```
用户输入登录码 → AuthManager → FlowSync API → 获取authToken
authToken → WebSocketManager → 建立连接 → 认证成功
认证成功 → CloudSyncManager → 请求云端配置 → 同步到本地
本地配置变更 → CloudSyncManager → 同步到云端 → 通知其他设备
```

### 后端要求

详细的后端API规范和WebSocket协议已在 `FlowSync_Backend_API_Specification.md` 中提供，包括：

- **登录API**：POST /api/login
- **WebSocket协议**：wss://flowsync.ykload.com/ws
- **消息类型**：认证、配置同步、实时更新等
- **数据库设计**：用户、认证、配置存储
- **安全要求**：加密传输、token管理等

## 已实现的文件

### 数据模型
- `app/src/main/java/cn/ykload/flowmix/data/FlowSyncCloudData.kt` - 云端同步数据结构

### 网络层
- `app/src/main/java/cn/ykload/flowmix/network/FlowSyncApi.kt` - FlowSync API接口
- `app/src/main/java/cn/ykload/flowmix/network/NetworkManager.kt` - 网络管理器（已更新）

### 认证系统
- `app/src/main/java/cn/ykload/flowmix/auth/AuthManager.kt` - 认证管理器

### 同步系统
- `app/src/main/java/cn/ykload/flowmix/sync/WebSocketManager.kt` - WebSocket管理器
- `app/src/main/java/cn/ykload/flowmix/sync/CloudSyncManager.kt` - 云端同步管理器

### 用户界面
- `app/src/main/java/cn/ykload/flowmix/ui/screen/LoginScreen.kt` - 登录界面
- `app/src/main/java/cn/ykload/flowmix/viewmodel/LoginViewModel.kt` - 登录ViewModel
- `app/src/main/java/cn/ykload/flowmix/ui/screen/FlowSyncScreen.kt` - FlowSync界面（已更新）
- `app/src/main/java/cn/ykload/flowmix/viewmodel/FlowSyncViewModel.kt` - FlowSyncViewModel（已更新）

### 依赖配置
- `app/build.gradle.kts` - 添加了WebSocket依赖

## 使用流程

### 用户首次使用
1. 打开FlowSync界面
2. 点击"登录账号"按钮
3. 输入从QQ Bot获取的6位登录码
4. 登录成功后自动建立云端连接
5. 从云端同步已有配置（如果有）

### 日常使用
1. App启动时自动登录（如果有有效token）
2. 自动建立WebSocket连接
3. 配置变更时自动同步到云端
4. 其他设备的配置变更会实时推送到本设备

### 设备切换
1. 在新设备上登录相同账号
2. 自动从云端获取所有设备配置
3. 当前设备配置自动应用
4. 配置变更在所有设备间实时同步

## 待完成的工作

### 后端开发
1. 实现登录API和用户管理
2. 实现WebSocket服务器和消息处理
3. 实现数据库设计和配置存储
4. 部署到生产环境

### 前端完善
1. 在DeviceConfigManager中添加批量更新方法
2. 实现单个设备配置的实时更新
3. 添加网络状态监听和离线提示
4. 完善错误处理和用户反馈

### 测试验证
1. 单元测试和集成测试
2. 多设备同步测试
3. 网络异常恢复测试
4. 性能和稳定性测试

## 安全考虑

1. **传输安全**：使用HTTPS/WSS加密传输
2. **认证安全**：authToken使用JWT格式，包含过期时间
3. **数据安全**：敏感配置数据加密存储
4. **访问控制**：严格的用户身份验证和授权

## 性能优化

1. **增量同步**：只传输变更的配置数据
2. **本地缓存**：优先使用本地配置，减少网络请求
3. **连接复用**：WebSocket长连接减少连接开销
4. **防抖机制**：避免频繁的配置同步请求

## 总结

FlowSync跨设备同步功能的设计和实现已经完成，提供了完整的技术架构和详细的实现方案。前端代码已经就绪，后端开发者可以根据提供的API规范进行开发。整个系统设计考虑了安全性、性能和用户体验，能够为用户提供无缝的跨设备音频配置同步体验。
