package cn.ykload.flowmix

import android.content.Intent
import android.media.audiofx.AudioEffect
import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import cn.ykload.flowmix.permission.PermissionManager
import cn.ykload.flowmix.ui.screen.MainNavigationScreen
import cn.ykload.flowmix.ui.theme.FlowmixTheme
import cn.ykload.flowmix.viewmodel.MainViewModel

/**
 * 均衡器Activity
 * 
 * 处理来自其他应用的音频效果控制请求
 * 当其他音乐播放器调用系统均衡器时，会启动此Activity
 */
class EqualizerActivity : ComponentActivity() {

    companion object {
        private const val TAG = "EqualizerActivity"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        Log.d(TAG, "EqualizerActivity启动")
        Log.d(TAG, "Intent action: ${intent.action}")
        Log.d(TAG, "Intent extras: ${intent.extras}")

        // 处理音频效果控制意图
        handleAudioEffectIntent(intent)

        setContent {
            FlowmixTheme {
                EqualizerScreen()
            }
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        Log.d(TAG, "收到新的Intent: ${intent.action}")
        handleAudioEffectIntent(intent)
    }

    /**
     * 处理音频效果控制意图
     */
    private fun handleAudioEffectIntent(intent: Intent) {
        when (intent.action) {
            AudioEffect.ACTION_OPEN_AUDIO_EFFECT_CONTROL_SESSION -> {
                handleOpenAudioEffectSession(intent)
            }
            AudioEffect.ACTION_CLOSE_AUDIO_EFFECT_CONTROL_SESSION -> {
                handleCloseAudioEffectSession(intent)
            }
            AudioEffect.ACTION_DISPLAY_AUDIO_EFFECT_CONTROL_PANEL -> {
                handleDisplayAudioEffectPanel(intent)
            }
        }
    }

    /**
     * 处理打开音频效果控制会话
     */
    private fun handleOpenAudioEffectSession(intent: Intent) {
        val audioSession = intent.getIntExtra(AudioEffect.EXTRA_AUDIO_SESSION, -1)
        val packageName = intent.getStringExtra(AudioEffect.EXTRA_PACKAGE_NAME)
        val contentType = intent.getIntExtra(AudioEffect.EXTRA_CONTENT_TYPE, AudioEffect.CONTENT_TYPE_MUSIC)
        
        Log.d(TAG, "打开音频效果控制会话")
        Log.d(TAG, "音频会话ID: $audioSession")
        Log.d(TAG, "调用包名: $packageName")
        Log.d(TAG, "内容类型: $contentType")
        
        // 由于我们使用全局音频控制，这里只需要记录信息
        // 实际的音频效果已经通过DynamicsProcessing应用到全局音频输出
    }

    /**
     * 处理关闭音频效果控制会话
     */
    private fun handleCloseAudioEffectSession(intent: Intent) {
        val audioSession = intent.getIntExtra(AudioEffect.EXTRA_AUDIO_SESSION, -1)
        val packageName = intent.getStringExtra(AudioEffect.EXTRA_PACKAGE_NAME)
        
        Log.d(TAG, "关闭音频效果控制会话")
        Log.d(TAG, "音频会话ID: $audioSession")
        Log.d(TAG, "调用包名: $packageName")
        
        // 由于我们使用全局音频控制，这里只需要记录信息
    }

    /**
     * 处理显示音频效果控制面板
     */
    private fun handleDisplayAudioEffectPanel(intent: Intent) {
        val audioSession = intent.getIntExtra(AudioEffect.EXTRA_AUDIO_SESSION, -1)
        val packageName = intent.getStringExtra(AudioEffect.EXTRA_PACKAGE_NAME)
        val contentType = intent.getIntExtra(AudioEffect.EXTRA_CONTENT_TYPE, AudioEffect.CONTENT_TYPE_MUSIC)
        
        Log.d(TAG, "显示音频效果控制面板")
        Log.d(TAG, "音频会话ID: $audioSession")
        Log.d(TAG, "调用包名: $packageName")
        Log.d(TAG, "内容类型: $contentType")
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun EqualizerScreen() {
    val context = LocalContext.current
    val viewModel: MainViewModel = viewModel { MainViewModel(context.applicationContext as android.app.Application) }
    val permissionManager = remember { PermissionManager(context) }

    Scaffold(
        topBar = {
            CenterAlignedTopAppBar(
                title = {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Center
                    ) {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_flowmix),
                            contentDescription = null,
                            modifier = Modifier.size(24.dp),
                            tint = MaterialTheme.colorScheme.primary
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "Flowmix 均衡器",
                            style = MaterialTheme.typography.titleLarge,
                            fontWeight = FontWeight.Bold
                        )
                    }
                },
                navigationIcon = {
                    IconButton(
                        onClick = { 
                            (context as? ComponentActivity)?.finish()
                        }
                    ) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 显示提示信息
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_flowmix),
                        contentDescription = null,
                        modifier = Modifier.size(48.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    Text(
                        text = "Flowmix 全局均衡器",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        textAlign = TextAlign.Center
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Text(
                        text = "Flowmix 使用全局音频处理技术，为所有应用提供统一的音频均衡效果。",
                        style = MaterialTheme.typography.bodyMedium,
                        textAlign = TextAlign.Center,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // 嵌入主要的均衡器界面
            MainNavigationScreen(
                viewModel = viewModel,
                permissionManager = permissionManager,
                modifier = Modifier.weight(1f)
            )
        }
    }
}
