package cn.ykload.flowmix.ui.component

import androidx.compose.animation.core.EaseInOutCubic
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.animate
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.nativeCanvas
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.input.pointer.changedToUp
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cn.ykload.flowmix.data.AutoEqData
import cn.ykload.flowmix.data.EqBand
import cn.ykload.flowmix.ui.theme.createLexendTypeface
import kotlinx.coroutines.delay
import kotlin.math.exp
import kotlin.math.ln
import kotlin.math.log10
import kotlin.math.roundToInt

/**
 * EQ曲线图组件
 */
@Composable
fun EqCurveChart(
    eqData: AutoEqData,
    isLoudnessCompensationEnabled: Boolean = false,
    globalGain: Float = 0f,
    visibleBandRange: IntRange? = null,
    modifier: Modifier = Modifier,
    height: androidx.compose.ui.unit.Dp = 280.dp,
    showChartLabel: Boolean = false,
    chartLabelText: String = "AutoEq曲线",
    onPageEntered: Boolean = false,
    onChartClick: (() -> Unit)? = null
) {
    val density = LocalDensity.current
    val context = LocalContext.current

    // 使用Material Design主题色
    val curveColor = MaterialTheme.colorScheme.primary
    val backgroundColor = MaterialTheme.colorScheme.surfaceVariant
    val gridColor = MaterialTheme.colorScheme.outline
    val textColor = MaterialTheme.colorScheme.onSurfaceVariant

    // 图表标签动画状态
    var labelAnimationProgress by remember { mutableStateOf(0f) }
    var labelPositionProgress by remember { mutableStateOf(0f) }
    var labelScaleProgress by remember { mutableStateOf(0f) }

    // 当页面进入时触发标签动画
    LaunchedEffect(onPageEntered) {
        if (onPageEntered && showChartLabel) {
            // 第一阶段：标签在中央以大字号显示
            animate(
                initialValue = 0f,
                targetValue = 1f,
                animationSpec = spring(
                    dampingRatio = Spring.DampingRatioMediumBouncy,
                    stiffness = Spring.StiffnessMedium
                )
            ) { value, _ ->
                labelScaleProgress = value
            }

            delay(800) // 在中央停留0.8秒

            // 第二阶段：移动到右上角并缩小
            animate(
                initialValue = 0f,
                targetValue = 1f,
                animationSpec = tween(
                    durationMillis = 600,
                    easing = EaseInOutCubic
                )
            ) { value, _ ->
                labelPositionProgress = value
                labelAnimationProgress = value
            }
        }
    }

    // 长按拖拽状态
    var dragPosition by remember { mutableStateOf<Offset?>(null) }
    var isDragging by remember { mutableStateOf(false) }
    var isLongPressActivated by remember { mutableStateOf(false) }

    // 自动重置长按状态
    LaunchedEffect(isLongPressActivated) {
        if (isLongPressActivated) {
            delay(5000) // 5秒后自动重置
            if (isLongPressActivated) {
                isDragging = false
                dragPosition = null
                isLongPressActivated = false
            }
        }
    }

    // EQ数据变化动画
    var displayEqData by remember { mutableStateOf(eqData) }
    var targetEqData by remember { mutableStateOf(eqData) }
    var flowEqAnimationProgress by remember { mutableStateOf(1f) }
    var isFlowEqAnimating by remember { mutableStateOf(false) }

    // 当EQ数据变化时触发动画
    LaunchedEffect(eqData) {
        if (eqData != displayEqData) {
            // EQ数据变化：从当前状态平滑过渡到新状态
            targetEqData = eqData
            isFlowEqAnimating = true
            flowEqAnimationProgress = 0f

            // 根据变化类型选择动画时长
            val animationDuration = if (eqData.name == "FlowEq") {
                2000 // FlowEq使用2秒动画
            } else {
                1000 // 其他EQ变化使用1秒动画
            }

            // 启动动画
            animate(
                initialValue = 0f,
                targetValue = 1f,
                animationSpec = tween(
                    durationMillis = animationDuration,
                    easing = EaseInOutCubic
                )
            ) { value, _ ->
                flowEqAnimationProgress = value
            }

            // 动画完成后更新显示数据 - 原子性更新避免闪现
            displayEqData = targetEqData
            flowEqAnimationProgress = 1f
            isFlowEqAnimating = false
        } else if (!isFlowEqAnimating) {
            // 数据相同或非动画状态，直接更新
            displayEqData = eqData
            targetEqData = eqData
            flowEqAnimationProgress = 1f
        }
    }

    // 计算补偿值和补偿后的EQ数据
    val compensationValue = remember(eqData) {
        eqData.calculateLoudnessCompensation()
    }

    val compensatedEqData = remember(eqData, compensationValue) {
        eqData.withLoudnessCompensation(compensationValue)
    }

    // 计算整体增益调节后的EQ数据
    val globalGainEqData = remember(eqData, globalGain) {
        eqData.withGlobalGain(globalGain)
    }

    // 确定目标增益值：等响度补偿优先，否则使用整体增益
    val targetGainValue = if (isLoudnessCompensationEnabled) compensationValue else globalGain
    val hasGainAdjustment = isLoudnessCompensationEnabled || globalGain != 0f

    // 动画状态 - 用于曲线移动动画
    val animationProgress by animateFloatAsState(
        targetValue = if (hasGainAdjustment) 1f else 0f,
        animationSpec = tween(
            durationMillis = 1000,
            easing = FastOutSlowInEasing
        ),
        label = "gain_adjustment_animation"
    )

    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(height)
            .clip(RoundedCornerShape(25.dp))
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        backgroundColor,
                        backgroundColor.copy(alpha = 0.8f)
                    )
                )
            )
    ) {
        Canvas(
            modifier = Modifier
                .fillMaxSize()
                .pointerInput(Unit) {
                    detectTapGestures(
                        onLongPress = { offset ->
                            isLongPressActivated = true
                            isDragging = true
                            dragPosition = offset
                        },
                        onTap = { offset ->
                            // 如果没有长按激活状态，则触发点击回调
                            if (!isLongPressActivated) {
                                onChartClick?.invoke()
                            } else {
                                // 点击时重置拖拽状态
                                isDragging = false
                                dragPosition = null
                                isLongPressActivated = false
                            }
                        }
                    )
                }
                .pointerInput(Unit) {
                    // 处理长按后的拖拽
                    awaitPointerEventScope {
                        while (true) {
                            val event = awaitPointerEvent()
                            if (isLongPressActivated) {
                                event.changes.forEach { change ->
                                    if (change.pressed) {
                                        // 更新拖拽位置
                                        dragPosition = change.position
                                        change.consume()
                                    } else if (change.changedToUp()) {
                                        // 手指抬起，结束拖拽
                                        isDragging = false
                                        dragPosition = null
                                        isLongPressActivated = false
                                        change.consume()
                                    }
                                }
                            }
                        }
                    }
                }
        ) {
            drawEqChart(
                originalEqData = eqData,
                displayEqData = displayEqData,
                targetEqData = targetEqData,
                compensatedEqData = compensatedEqData,
                globalGainEqData = globalGainEqData,
                compensationValue = compensationValue,
                targetGainValue = targetGainValue,
                isLoudnessCompensationEnabled = isLoudnessCompensationEnabled,
                animationProgress = animationProgress,
                flowEqAnimationProgress = flowEqAnimationProgress,
                isFlowEqAnimating = isFlowEqAnimating,
                curveColor = curveColor,
                gridColor = gridColor,
                textColor = textColor,
                backgroundColor = backgroundColor,
                dragPosition = dragPosition,
                isDragging = isDragging,
                visibleBandRange = visibleBandRange,
                density = density,
                context = context
            )

            // 绘制图表标签
            if (showChartLabel) {
                drawChartLabel(
                    labelText = chartLabelText,
                    labelScaleProgress = labelScaleProgress,
                    labelPositionProgress = labelPositionProgress,
                    labelAnimationProgress = labelAnimationProgress,
                    textColor = textColor,
                    backgroundColor = backgroundColor,
                    density = density,
                    context = context
                )
            }
        }
    }
}

/**
 * 绘制EQ图表
 */
private fun DrawScope.drawEqChart(
    originalEqData: AutoEqData,
    displayEqData: AutoEqData,
    targetEqData: AutoEqData,
    compensatedEqData: AutoEqData,
    globalGainEqData: AutoEqData?,
    compensationValue: Float,
    targetGainValue: Float,
    isLoudnessCompensationEnabled: Boolean,
    animationProgress: Float,
    flowEqAnimationProgress: Float,
    isFlowEqAnimating: Boolean,
    curveColor: Color,
    gridColor: Color,
    textColor: Color,
    backgroundColor: Color,
    dragPosition: Offset?,
    isDragging: Boolean,
    visibleBandRange: IntRange?,
    density: androidx.compose.ui.unit.Density,
    context: android.content.Context
) {
    val originalBands = originalEqData.bands
    val compensatedBands = compensatedEqData.bands
    if (originalBands.isEmpty()) return

    // 图表占据整个区域
    val chartWidth = size.width
    val chartHeight = size.height
    val chartLeft = 0f
    val chartTop = 0f

    // 固定增益范围，参考图片样式
    val minGain = -20f
    val maxGain = 10f

    // 固定频率范围
    val minFreq = 20f
    val maxFreq = 20000f

    // 绘制网格线
    drawGrid(
        chartLeft = chartLeft,
        chartTop = chartTop,
        chartWidth = chartWidth,
        chartHeight = chartHeight,
        minGain = minGain,
        maxGain = maxGain,
        gridColor = gridColor
    )

    // 绘制标签（覆盖在图表上方）
    drawLabels(
        chartLeft = chartLeft,
        chartTop = chartTop,
        chartWidth = chartWidth,
        chartHeight = chartHeight,
        minGain = minGain,
        maxGain = maxGain,
        textColor = textColor,
        density = density,
        context = context
    )

    // 计算动画插值的EQ数据（用于FlowEq动画）
    val animatedBands = if (isFlowEqAnimating && flowEqAnimationProgress < 1f) {
        // FlowEq动画：从displayEqData平滑过渡到targetEqData
        val currentBands = displayEqData.bands
        val targetBands = targetEqData.bands

        // 创建插值后的频段数据
        targetBands.map { targetBand ->
            // 在currentBands中查找相同频率的频段
            val currentBand = currentBands.find { it.frequency == targetBand.frequency }
            if (currentBand != null) {
                // 找到匹配的频段，进行插值
                EqBand(
                    frequency = targetBand.frequency,
                    gain = currentBand.gain + (targetBand.gain - currentBand.gain) * flowEqAnimationProgress
                )
            } else {
                // 没有找到匹配的频段，使用目标频段（从0开始插值）
                EqBand(
                    frequency = targetBand.frequency,
                    gain = targetBand.gain * flowEqAnimationProgress
                )
            }
        }
    } else if (flowEqAnimationProgress >= 1f && targetEqData.bands.isNotEmpty()) {
        // 动画完成或接近完成，确保使用目标数据
        targetEqData.bands
    } else {
        // 非动画状态，使用当前显示的数据
        displayEqData.bands
    }

    // 绘制背景原始EQ曲线（当有动画时变暗）
    val backgroundAlpha = if (animationProgress > 0f) 0.3f else 1f
    drawEqCurve(
        bands = animatedBands,
        chartLeft = chartLeft,
        chartTop = chartTop,
        chartWidth = chartWidth,
        chartHeight = chartHeight,
        minGain = minGain,
        maxGain = maxGain,
        curveColor = curveColor.copy(alpha = backgroundAlpha),
        showFill = animationProgress == 0f // 只在没有动画时显示填充
    )

    // 绘制动画中的移动曲线（从原位置移动到增益调节位置）
    if (animationProgress > 0f) {
        // 计算增益调节后的动画数据
        val animatedAdjustedBands = if (isFlowEqAnimating && flowEqAnimationProgress < 1f) {
            // 对于FlowEq，使用插值后的数据计算增益调节
            animatedBands.map { band ->
                EqBand(
                    frequency = band.frequency,
                    gain = (band.gain + targetGainValue).coerceIn(-30f, 30f)
                )
            }
        } else {
            // 根据当前状态选择目标数据
            if (isLoudnessCompensationEnabled) {
                compensatedBands
            } else {
                globalGainEqData?.bands ?: compensatedBands
            }
        }

        drawAnimatedEqCurve(
            originalBands = animatedBands,
            compensatedBands = animatedAdjustedBands,
            animationProgress = animationProgress,
            chartLeft = chartLeft,
            chartTop = chartTop,
            chartWidth = chartWidth,
            chartHeight = chartHeight,
            minGain = minGain,
            maxGain = maxGain,
            curveColor = curveColor, // 使用正常的原始颜色
            showFill = true
        )
    }

    // 绘制可见频段高亮 - 只在有可见范围且调节器展开时显示
    if (visibleBandRange != null && originalBands.isNotEmpty()) {
        drawVisibleBandHighlight(
            visibleBandRange = visibleBandRange,
            eqBands = originalBands,
            chartLeft = chartLeft,
            chartTop = chartTop,
            chartWidth = chartWidth,
            chartHeight = chartHeight,
            minFreq = minFreq,
            maxFreq = maxFreq,
            curveColor = curveColor
        )
    }

    // 绘制拖拽指示器
    if (isDragging && dragPosition != null) {
        // 使用当前主要显示的曲线数据
        val currentEqData = if (animationProgress > 0.5f) compensatedEqData else originalEqData

        drawDragIndicator(
            dragPosition = dragPosition,
            chartLeft = chartLeft,
            chartTop = chartTop,
            chartWidth = chartWidth,
            chartHeight = chartHeight,
            minGain = minGain,
            maxGain = maxGain,
            eqData = currentEqData,
            curveColor = curveColor, // 始终使用原始颜色
            textColor = textColor,
            backgroundColor = backgroundColor,
            density = density,
            context = context
        )
    }
}

/**
 * 绘制网格线
 */
private fun DrawScope.drawGrid(
    chartLeft: Float,
    chartTop: Float,
    chartWidth: Float,
    chartHeight: Float,
    minGain: Float,
    maxGain: Float,
    gridColor: Color
) {
    val lightGridColor = gridColor.copy(alpha = 0.2f)
    val zeroLineColor = gridColor.copy(alpha = 0.5f)

    // 水平网格线 (增益) - 参考图片样式，每5dB一条线，但不显示-20和+10
    val gainStep = 5f
    var gain = (minGain / gainStep).roundToInt() * gainStep
    while (gain <= maxGain) {
        // 跳过-20和+10的线条
        if (gain != -20f && gain != 10f) {
            val y = chartTop + chartHeight * (1f - (gain - minGain) / (maxGain - minGain))
            val color = if (gain == 0f) zeroLineColor else lightGridColor
            val strokeWidth = if (gain == 0f) 1.dp.toPx() else 0.5.dp.toPx()

            drawLine(
                color = color,
                start = Offset(chartLeft, y),
                end = Offset(chartLeft + chartWidth, y),
                strokeWidth = strokeWidth
            )
        }

        gain += gainStep
    }

    // 垂直网格线 (频率) - 参考图片的频率点
    val freqMarkers = listOf(54f, 148f, 403f, 1096f, 2980f, 8103f)
    freqMarkers.forEach { freq ->
        val logFreq = ln(freq.coerceIn(20f, 20000f))
        val logMin = ln(20f)
        val logMax = ln(20000f)
        val x = chartLeft + chartWidth * (logFreq - logMin) / (logMax - logMin)

        drawLine(
            color = lightGridColor,
            start = Offset(x, chartTop),
            end = Offset(x, chartTop + chartHeight),
            strokeWidth = 0.5.dp.toPx()
        )
    }
}

/**
 * 绘制标签（覆盖在图表上方）
 */
private fun DrawScope.drawLabels(
    chartLeft: Float,
    chartTop: Float,
    chartWidth: Float,
    chartHeight: Float,
    minGain: Float,
    maxGain: Float,
    textColor: Color,
    density: androidx.compose.ui.unit.Density,
    context: android.content.Context
) {
    // Y轴标签 (增益值) - 显示在图表左上角区域，但不显示-20和+10
    val gainStep = 5f
    var gain = (minGain / gainStep).roundToInt() * gainStep
    while (gain <= maxGain) {
        // 跳过-20和+10的标签
        if (gain != -20f && gain != 10f) {
            val y = chartTop + chartHeight * (1f - (gain - minGain) / (maxGain - minGain))

            drawContext.canvas.nativeCanvas.apply {
                val lexendTypeface = createLexendTypeface(context)
                val textPaint = android.graphics.Paint().apply {
                    color = textColor.toArgb()
                    textSize = 11.sp.toPx()
                    typeface = lexendTypeface
                    textAlign = android.graphics.Paint.Align.LEFT
                }
                drawText(
                    gain.toInt().toString(),
                    chartLeft + 8.dp.toPx(),
                    y - 4.dp.toPx(),
                    textPaint
                )
            }
        }

        gain += gainStep
    }

    // X轴标签 (频率值) - 显示在图表底部
    val freqMarkers = listOf(54f, 148f, 403f, 1096f, 2980f, 8103f)
    freqMarkers.forEach { freq ->
        val logFreq = ln(freq.coerceIn(20f, 20000f))
        val logMin = ln(20f)
        val logMax = ln(20000f)
        val x = chartLeft + chartWidth * (logFreq - logMin) / (logMax - logMin)

        drawContext.canvas.nativeCanvas.apply {
            val lexendTypeface = createLexendTypeface(context)
            val textPaint = android.graphics.Paint().apply {
                color = textColor.toArgb()
                textSize = 11.sp.toPx()
                typeface = lexendTypeface
                textAlign = android.graphics.Paint.Align.CENTER
            }
            drawText(
                freq.toInt().toString(),
                x,
                chartTop + chartHeight - 8.dp.toPx(),
                textPaint
            )
        }
    }
}

/**
 * 绘制EQ曲线
 */
private fun DrawScope.drawEqCurve(
    bands: List<EqBand>,
    chartLeft: Float,
    chartTop: Float,
    chartWidth: Float,
    chartHeight: Float,
    minGain: Float,
    maxGain: Float,
    curveColor: Color,
    showFill: Boolean = true
) {
    if (bands.size < 2) return

    val path = Path()
    val fillPath = Path()

    // 计算点的坐标
    val points = bands.map { band ->
        val logFreq = ln(band.frequency.coerceIn(20f, 20000f))
        val logMin = ln(20f)
        val logMax = ln(20000f)
        val x = chartLeft + chartWidth * (logFreq - logMin) / (logMax - logMin)
        val y = chartTop + chartHeight * (1f - (band.gain - minGain) / (maxGain - minGain))
        Offset(x, y)
    }

    // 创建平滑曲线
    if (points.isNotEmpty()) {
        path.moveTo(points[0].x, points[0].y)
        fillPath.moveTo(points[0].x, points[0].y)

        // 创建流畅的样条曲线
        if (points.size > 2) {
            // 使用Catmull-Rom样条曲线创建更自然的曲线
            for (i in 1 until points.size) {
                val p0 = if (i > 1) points[i - 2] else points[i - 1]
                val p1 = points[i - 1]
                val p2 = points[i]
                val p3 = if (i < points.size - 1) points[i + 1] else points[i]

                // 计算控制点
                val tension = 0.4f // 稍微减少张力，让曲线更平滑
                val control1X = p1.x + (p2.x - p0.x) * tension / 6f
                val control1Y = p1.y + (p2.y - p0.y) * tension / 6f
                val control2X = p2.x - (p3.x - p1.x) * tension / 6f
                val control2Y = p2.y - (p3.y - p1.y) * tension / 6f

                path.cubicTo(control1X, control1Y, control2X, control2Y, p2.x, p2.y)
                fillPath.cubicTo(control1X, control1Y, control2X, control2Y, p2.x, p2.y)
            }
        } else {
            // 如果点数较少，使用直线连接
            for (i in 1 until points.size) {
                path.lineTo(points[i].x, points[i].y)
                fillPath.lineTo(points[i].x, points[i].y)
            }
        }

        // 绘制填充区域（如果启用）
        if (showFill) {
            // 创建填充区域 - 从曲线到图表底部，参考图片样式
            fillPath.lineTo(points.last().x, chartTop + chartHeight)
            fillPath.lineTo(points.first().x, chartTop + chartHeight)
            fillPath.close()

            // 绘制渐变填充区域 - 使用主题色的渐变效果
            val gradient = Brush.verticalGradient(
                colors = listOf(
                    curveColor.copy(alpha = 0.25f),   // 曲线处较浓
                    curveColor.copy(alpha = 0.12f),   // 中间
                    curveColor.copy(alpha = 0.04f),   // 底部很淡
                    Color.Transparent                 // 完全透明
                ),
                startY = chartTop,
                endY = chartTop + chartHeight
            )

            drawPath(
                path = fillPath,
                brush = gradient
            )
        }

        // 绘制曲线 - 参考图片的粗线条样式
        drawPath(
            path = path,
            color = curveColor,
            style = Stroke(
                width = 2.dp.toPx(),
                cap = StrokeCap.Round,
                join = StrokeJoin.Round
            )
        )
    }
}

/**
 * 绘制拖拽指示器
 */
private fun DrawScope.drawDragIndicator(
    dragPosition: Offset,
    chartLeft: Float,
    chartTop: Float,
    chartWidth: Float,
    chartHeight: Float,
    minGain: Float,
    maxGain: Float,
    eqData: AutoEqData,
    curveColor: Color,
    textColor: Color,
    backgroundColor: Color,
    density: androidx.compose.ui.unit.Density,
    context: android.content.Context
) {
    val x = dragPosition.x

    // 确保拖拽位置在图表范围内
    if (x < chartLeft || x > chartLeft + chartWidth) {
        return
    }

    // 计算对应的频率
    val logMin = ln(20f)
    val logMax = ln(20000f)
    val logFreq = logMin + (x - chartLeft) / chartWidth * (logMax - logMin)
    val frequency = exp(logFreq).coerceIn(20f, 20000f)

    // 找到曲线上对应频率的增益值
    val curveGain = findGainAtFrequency(eqData.bands, frequency)
    val curveY = chartTop + chartHeight * (1f - (curveGain - minGain) / (maxGain - minGain))

    // 绘制垂直指示线 - 只绘制到曲线点，使用适合暗色主题的颜色
    val indicatorColor = textColor.copy(alpha = 0.4f)
    drawLine(
        color = indicatorColor,
        start = Offset(x, curveY),
        end = Offset(x, chartTop + chartHeight),
        strokeWidth = 1.dp.toPx()
    )

    // 绘制水平指示线 - 使用更淡的颜色
    drawLine(
        color = curveColor.copy(alpha = 0.3f),
        start = Offset(chartLeft, curveY),
        end = Offset(x, curveY),
        strokeWidth = 1.dp.toPx()
    )

    // 绘制曲线上的交叉点圆圈
    drawCircle(
        color = curveColor,
        radius = 2.5.dp.toPx(),
        center = Offset(x, curveY),
        style = Stroke(width = 2.dp.toPx())
    )

    // 内部填充圆圈 - 使用背景色
    drawCircle(
        color = backgroundColor,
        radius = 2.5.dp.toPx(),
        center = Offset(x, curveY)
    )

    // 计算最佳标签位置（避让曲线）
    val optimalLabelPosition = calculateOptimalEqLabelPosition(
        curveY = curveY,
        chartLeft = chartLeft,
        chartTop = chartTop,
        chartWidth = chartWidth,
        chartHeight = chartHeight,
        dragX = x,
        density = density
    )

    // 绘制信息标签
    drawInfoLabel(
        frequency = frequency,
        gain = curveGain,
        position = optimalLabelPosition,
        chartLeft = chartLeft,
        chartTop = chartTop,
        chartWidth = chartWidth,
        chartHeight = chartHeight,
        textColor = textColor,
        backgroundColor = backgroundColor,
        density = density,
        context = context
    )
}

/**
 * 计算最佳标签位置以避让曲线
 */
private fun calculateOptimalEqLabelPosition(
    curveY: Float,
    chartLeft: Float,
    chartTop: Float,
    chartWidth: Float,
    chartHeight: Float,
    dragX: Float,
    density: androidx.compose.ui.unit.Density
): Offset {
    val labelHeight = with(density) { 40.dp.toPx() }
    val margin = with(density) { 16.dp.toPx() }

    // 计算标签在曲线上方和下方的位置
    val aboveY = curveY - labelHeight - margin
    val belowY = curveY + margin

    // 选择最佳位置
    val labelY = when {
        // 如果上方有足够空间，优先放在上方
        aboveY >= chartTop -> aboveY
        // 如果下方有足够空间，放在下方
        belowY + labelHeight <= chartTop + chartHeight -> belowY
        // 如果都没有足够空间，选择距离边界更远的位置
        else -> {
            val topDistance = curveY - chartTop
            val bottomDistance = chartTop + chartHeight - curveY
            if (topDistance > bottomDistance) {
                chartTop + margin
            } else {
                chartTop + chartHeight - labelHeight - margin
            }
        }
    }

    return Offset(dragX, labelY.coerceIn(chartTop, chartTop + chartHeight - labelHeight))
}

/**
 * 根据频率查找对应的增益值（使用插值）
 */
private fun findGainAtFrequency(bands: List<EqBand>, targetFrequency: Float): Float {
    if (bands.isEmpty()) return 0f

    // 如果目标频率小于最小频率，返回第一个频段的增益
    if (targetFrequency <= bands.first().frequency) {
        return bands.first().gain
    }

    // 如果目标频率大于最大频率，返回最后一个频段的增益
    if (targetFrequency >= bands.last().frequency) {
        return bands.last().gain
    }

    // 找到目标频率所在的区间并进行线性插值
    for (i in 0 until bands.size - 1) {
        val band1 = bands[i]
        val band2 = bands[i + 1]

        if (targetFrequency >= band1.frequency && targetFrequency <= band2.frequency) {
            // 在对数频率空间进行插值
            val logFreq1 = ln(band1.frequency)
            val logFreq2 = ln(band2.frequency)
            val logTargetFreq = ln(targetFrequency)

            val ratio = (logTargetFreq - logFreq1) / (logFreq2 - logFreq1)
            return band1.gain + ratio * (band2.gain - band1.gain)
        }
    }

    return 0f
}

/**
 * 绘制信息标签
 */
private fun DrawScope.drawInfoLabel(
    frequency: Float,
    gain: Float,
    position: Offset,
    chartLeft: Float,
    chartTop: Float,
    chartWidth: Float,
    chartHeight: Float,
    textColor: Color,
    backgroundColor: Color,
    density: androidx.compose.ui.unit.Density,
    context: android.content.Context
) {
    val freqText = when {
        frequency < 1000 -> "${frequency.toInt()}Hz"
        else -> "${(frequency / 1000).let { if (it == it.toInt().toFloat()) it.toInt().toString() else "%.1f".format(it) }}kHz"
    }
    val gainText = "${if (gain >= 0) "+" else ""}${"%.1f".format(gain)}dB"
    val labelText = "$freqText, $gainText"

    // 计算标签位置（避免超出边界）
    val labelWidth = 120.dp.toPx()
    val labelHeight = 40.dp.toPx()
    val padding = 8.dp.toPx()

    var labelX = position.x - labelWidth / 2
    var labelY = position.y // 使用传入的position作为顶部位置

    // 调整X位置避免超出边界
    if (labelX < chartLeft) labelX = chartLeft + 8.dp.toPx()
    if (labelX + labelWidth > chartLeft + chartWidth) labelX = chartLeft + chartWidth - labelWidth - 8.dp.toPx()

    // 确保Y位置在图表范围内
    labelY = labelY.coerceIn(chartTop + 8.dp.toPx(), chartTop + chartHeight - labelHeight - 8.dp.toPx())

    // 绘制标签背景 - 使用Material Design的surface颜色
    drawRoundRect(
        color = backgroundColor.copy(alpha = 0.95f),
        topLeft = Offset(labelX, labelY),
        size = androidx.compose.ui.geometry.Size(labelWidth, labelHeight),
        cornerRadius = androidx.compose.ui.geometry.CornerRadius(25.dp.toPx()),
        style = androidx.compose.ui.graphics.drawscope.Fill
    )

    // 绘制标签边框
    drawRoundRect(
        color = textColor.copy(alpha = 0.2f),
        topLeft = Offset(labelX, labelY),
        size = androidx.compose.ui.geometry.Size(labelWidth, labelHeight),
        cornerRadius = androidx.compose.ui.geometry.CornerRadius(25.dp.toPx()),
        style = Stroke(width = 1.dp.toPx())
    )

    // 绘制文字
    drawContext.canvas.nativeCanvas.apply {
        val lexendTypeface = createLexendTypeface(context)
        val textPaint = android.graphics.Paint().apply {
            color = textColor.toArgb()
            textSize = with(density) { 12.sp.toPx() }
            typeface = lexendTypeface
            textAlign = android.graphics.Paint.Align.CENTER
            isAntiAlias = true
        }
        drawText(
            labelText,
            labelX + labelWidth / 2,
            labelY + labelHeight / 2 + 4.dp.toPx(),
            textPaint
        )
    }
}

/**
 * 绘制动画中的EQ曲线（从原位置移动到补偿位置）
 */
private fun DrawScope.drawAnimatedEqCurve(
    originalBands: List<EqBand>,
    compensatedBands: List<EqBand>,
    animationProgress: Float,
    chartLeft: Float,
    chartTop: Float,
    chartWidth: Float,
    chartHeight: Float,
    minGain: Float,
    maxGain: Float,
    curveColor: Color,
    showFill: Boolean = true
) {
    if (originalBands.size < 2 || compensatedBands.size != originalBands.size) return

    val path = Path()
    val fillPath = Path()

    // 计算插值后的点坐标（从原始位置移动到补偿位置）
    val animatedPoints = originalBands.mapIndexed { index, originalBand ->
        val compensatedBand = compensatedBands[index]

        // 频率保持不变
        val frequency = originalBand.frequency

        // 增益在原始值和补偿值之间插值
        val animatedGain = originalBand.gain + (compensatedBand.gain - originalBand.gain) * animationProgress

        // 计算屏幕坐标
        val logFreq = ln(frequency.coerceIn(20f, 20000f))
        val logMin = ln(20f)
        val logMax = ln(20000f)
        val x = chartLeft + chartWidth * (logFreq - logMin) / (logMax - logMin)
        val y = chartTop + chartHeight * (1f - (animatedGain - minGain) / (maxGain - minGain))
        Offset(x, y)
    }

    // 创建平滑曲线
    if (animatedPoints.isNotEmpty()) {
        path.moveTo(animatedPoints[0].x, animatedPoints[0].y)
        fillPath.moveTo(animatedPoints[0].x, animatedPoints[0].y)

        // 创建流畅的样条曲线
        if (animatedPoints.size > 2) {
            // 使用Catmull-Rom样条曲线创建更自然的曲线
            for (i in 1 until animatedPoints.size) {
                val p0 = if (i > 1) animatedPoints[i - 2] else animatedPoints[i - 1]
                val p1 = animatedPoints[i - 1]
                val p2 = animatedPoints[i]
                val p3 = if (i < animatedPoints.size - 1) animatedPoints[i + 1] else animatedPoints[i]

                // 计算控制点
                val tension = 0.4f
                val control1X = p1.x + (p2.x - p0.x) * tension / 6f
                val control1Y = p1.y + (p2.y - p0.y) * tension / 6f
                val control2X = p2.x - (p3.x - p1.x) * tension / 6f
                val control2Y = p2.y - (p3.y - p1.y) * tension / 6f

                path.cubicTo(control1X, control1Y, control2X, control2Y, p2.x, p2.y)
                fillPath.cubicTo(control1X, control1Y, control2X, control2Y, p2.x, p2.y)
            }
        } else {
            // 如果点数较少，使用直线连接
            for (i in 1 until animatedPoints.size) {
                path.lineTo(animatedPoints[i].x, animatedPoints[i].y)
                fillPath.lineTo(animatedPoints[i].x, animatedPoints[i].y)
            }
        }

        // 绘制填充区域（如果启用）
        if (showFill) {
            // 创建填充区域 - 从曲线到图表底部
            fillPath.lineTo(animatedPoints.last().x, chartTop + chartHeight)
            fillPath.lineTo(animatedPoints.first().x, chartTop + chartHeight)
            fillPath.close()

            // 绘制渐变填充区域
            val gradient = Brush.verticalGradient(
                colors = listOf(
                    curveColor.copy(alpha = 0.25f),   // 曲线处较浓
                    curveColor.copy(alpha = 0.12f),   // 中间
                    curveColor.copy(alpha = 0.04f),   // 底部很淡
                    Color.Transparent                 // 完全透明
                ),
                startY = chartTop,
                endY = chartTop + chartHeight
            )

            drawPath(
                path = fillPath,
                brush = gradient
            )
        }

        // 绘制曲线
        drawPath(
            path = path,
            color = curveColor,
            style = Stroke(
                width = 2.dp.toPx(),
                cap = StrokeCap.Round,
                join = StrokeJoin.Round
            )
        )
    }
}

/**
 * 绘制图表标签
 */
private fun DrawScope.drawChartLabel(
    labelText: String,
    labelScaleProgress: Float,
    labelPositionProgress: Float,
    labelAnimationProgress: Float,
    textColor: Color,
    backgroundColor: Color,
    density: androidx.compose.ui.unit.Density,
    context: android.content.Context
) {
    if (labelScaleProgress <= 0f) return

    val chartWidth = size.width
    val chartHeight = size.height
    val lexendTypeface = createLexendTypeface(context)

    // 主标题和副标题
    val mainTitle = labelText
    val subTitle = "点击修改 | 长按浏览"

    // 字体大小设置 - 主标题缩小一些
    val mainTitleLargeSize = 22.sp.toPx() // 从28sp减小到22sp
    val mainTitleSmallSize = 12.sp.toPx() // 从14sp减小到12sp
    val subTitleSize = 10.sp.toPx()

    // 计算当前主标题字体大小（通过缩放而不是改变字体大小）
    val currentMainTitleSize = mainTitleLargeSize // 保持字体大小不变
    val currentScale = 1f + (0.55f - 1f) * labelAnimationProgress // 从1.0缩放到0.55

    // 先测量文字尺寸用于计算目标位置
    val tempMainPaint = android.graphics.Paint().apply {
        textSize = mainTitleSmallSize
        typeface = lexendTypeface
    }
    val tempMainBounds = android.graphics.Rect()
    tempMainPaint.getTextBounds(mainTitle, 0, mainTitle.length, tempMainBounds)
    val finalMainTextWidth = tempMainBounds.width()

    // 计算标签位置
    val centerX = chartWidth / 2
    val centerY = chartHeight / 2
    // 目标位置：文字右边缘距离Card右边缘16dp
    val targetX = chartWidth - 16.dp.toPx() - finalMainTextWidth / 2
    val targetY = 20.dp.toPx() // 稍微下移一点

    // 插值计算当前位置
    val currentX = centerX + (targetX - centerX) * labelPositionProgress
    val currentY = centerY + (targetY - centerY) * labelPositionProgress

    // 计算透明度
    val mainAlpha = if (labelAnimationProgress < 1f) {
        1f
    } else {
        0.8f
    }

    // 副标题透明度 - 当主标题开始移动时淡出
    val subAlpha = if (labelPositionProgress > 0f) {
        (1f - labelPositionProgress).coerceAtLeast(0f)
    } else {
        1f
    }

    // 背景透明度 - 从1渐变到0
    val backgroundAlpha = if (labelAnimationProgress < 1f) {
        1f - labelAnimationProgress // 从1到0
    } else {
        0f // 动画完成后完全透明
    }

    // 绘制标签文字和背景
    drawContext.canvas.nativeCanvas.apply {
        val mainTextPaint = android.graphics.Paint().apply {
            color = textColor.copy(alpha = mainAlpha).toArgb()
            textSize = currentMainTitleSize
            typeface = lexendTypeface
            textAlign = android.graphics.Paint.Align.CENTER
            isAntiAlias = true
        }

        val subTextPaint = android.graphics.Paint().apply {
            color = textColor.copy(alpha = subAlpha).toArgb()
            textSize = subTitleSize
            typeface = lexendTypeface
            textAlign = android.graphics.Paint.Align.CENTER
            isAntiAlias = true
        }

        // 测量主标题和副标题尺寸
        val mainBounds = android.graphics.Rect()
        mainTextPaint.getTextBounds(mainTitle, 0, mainTitle.length, mainBounds)
        val mainTextWidth = mainBounds.width()
        val mainTextHeight = mainBounds.height()

        val subBounds = android.graphics.Rect()
        subTextPaint.getTextBounds(subTitle, 0, subTitle.length, subBounds)
        val subTextWidth = subBounds.width()
        val subTextHeight = subBounds.height()

        // 计算总体尺寸（包含两行文字）
        val totalWidth = maxOf(mainTextWidth, subTextWidth)
        val lineSpacing = 4.dp.toPx()
        val totalHeight = mainTextHeight + lineSpacing + subTextHeight

        // 计算实际绘制位置
        val actualX = currentX

        // 增大背景padding
        val padding = 12.dp.toPx() // 从8dp增加到12dp
        val backgroundLeft = actualX - totalWidth / 2 - padding
        val backgroundTop = currentY - totalHeight / 2 - padding
        val backgroundRight = actualX + totalWidth / 2 + padding
        val backgroundBottom = currentY + totalHeight / 2 + padding

        // 应用缩放
        save()
        scale(currentScale, currentScale, actualX, currentY)

        // 绘制圆角矩形背景
        val backgroundPaint = android.graphics.Paint().apply {
            color = backgroundColor.copy(alpha = backgroundAlpha).toArgb()
            isAntiAlias = true
        }
        val cornerRadius = 25.dp.toPx()
        drawRoundRect(
            backgroundLeft,
            backgroundTop,
            backgroundRight,
            backgroundBottom,
            cornerRadius,
            cornerRadius,
            backgroundPaint
        )

        // 绘制主标题 - 使用fontMetrics进行精确垂直居中
        val mainFontMetrics = mainTextPaint.fontMetrics
        val mainTextBaseline = currentY - totalHeight / 2 + mainTextHeight / 2 - (mainFontMetrics.ascent + mainFontMetrics.descent) / 2
        drawText(
            mainTitle,
            actualX,
            mainTextBaseline,
            mainTextPaint
        )

        // 绘制副标题（只在未开始移动时显示）
        if (subAlpha > 0f) {
            val subFontMetrics = subTextPaint.fontMetrics
            val subTextBaseline = currentY + totalHeight / 2 - subTextHeight / 2 - (subFontMetrics.ascent + subFontMetrics.descent) / 2
            drawText(
                subTitle,
                actualX,
                subTextBaseline,
                subTextPaint
            )
        }

        restore()
    }
}

/**
 * 绘制可见频段高亮
 */
private fun DrawScope.drawVisibleBandHighlight(
    visibleBandRange: IntRange,
    eqBands: List<EqBand>,
    chartLeft: Float,
    chartTop: Float,
    chartWidth: Float,
    chartHeight: Float,
    minFreq: Float,
    maxFreq: Float,
    curveColor: Color
) {
    if (eqBands.isEmpty() || visibleBandRange.isEmpty()) return

    // 确保索引在有效范围内
    val startIndex = visibleBandRange.first.coerceIn(0, eqBands.size - 1)
    val endIndex = visibleBandRange.last.coerceIn(0, eqBands.size - 1)

    if (startIndex >= eqBands.size || endIndex >= eqBands.size) return

    // 获取可见频段的频率范围
    val startFreq = eqBands[startIndex].frequency
    val endFreq = eqBands[endIndex].frequency

    // 计算频率在图表中的X坐标
    val startX = chartLeft + chartWidth * (log10(startFreq) - log10(minFreq)) / (log10(maxFreq) - log10(minFreq))
    val endX = chartLeft + chartWidth * (log10(endFreq) - log10(minFreq)) / (log10(maxFreq) - log10(minFreq))

    // 绘制高亮区域
    drawRect(
        color = curveColor.copy(alpha = 0.1f),
        topLeft = Offset(startX, chartTop),
        size = androidx.compose.ui.geometry.Size(endX - startX, chartHeight)
    )

    // 绘制边界线
    drawLine(
        color = curveColor.copy(alpha = 0.3f),
        start = Offset(startX, chartTop),
        end = Offset(startX, chartTop + chartHeight),
        strokeWidth = 2.dp.toPx()
    )
    drawLine(
        color = curveColor.copy(alpha = 0.3f),
        start = Offset(endX, chartTop),
        end = Offset(endX, chartTop + chartHeight),
        strokeWidth = 2.dp.toPx()
    )
}