package cn.ykload.flowmix

import androidx.compose.material3.MaterialTheme
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import cn.ykload.flowmix.data.AutoEqData
import cn.ykload.flowmix.data.EqBand
import cn.ykload.flowmix.ui.component.EqCurveChart
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

/**
 * EQ曲线图组件测试
 */
@RunWith(AndroidJUnit4::class)
class EqCurveChartTest {

    @get:Rule
    val composeTestRule = createComposeRule()

    @Test
    fun testEqCurveChartRendering() {
        // 创建测试数据
        val testBands = listOf(
            EqBand(20f, -5f),
            EqBand(100f, -2f),
            EqBand(500f, 0f),
            EqBand(1000f, 3f),
            EqBand(5000f, -1f),
            EqBand(10000f, 2f),
            EqBand(20000f, -3f)
        )
        val testEqData = AutoEqData(bands = testBands, name = "Test EQ")

        // 渲染组件
        composeTestRule.setContent {
            MaterialTheme {
                EqCurveChart(eqData = testEqData)
            }
        }

        // 验证组件能够正常渲染
        composeTestRule.waitForIdle()
    }

    @Test
    fun testEqCurveChartWithEmptyData() {
        // 创建空数据
        val emptyEqData = AutoEqData(bands = emptyList(), name = "Empty EQ")

        // 渲染组件
        composeTestRule.setContent {
            MaterialTheme {
                EqCurveChart(eqData = emptyEqData)
            }
        }

        // 验证组件能够处理空数据
        composeTestRule.waitForIdle()
    }
}
