# 等响度补偿功能

## 功能概述

为AutoEq界面添加了"等响度补偿"开关功能，开启后App会自动调整目前的AutoEq的整体增益数值，使得其与不使用AutoEq时的响度保持一致。

## 实现特性

### 1. 等响度补偿算法
- 使用A-weighting曲线近似计算响度补偿
- 基于ISO 226标准的A-weighting曲线
- 重点关注人耳敏感频段（1kHz附近）
- 计算加权平均增益作为补偿基准

### 2. 动画效果
- **开启等响度时**：
  - 原EQ曲线变暗作为背景层
  - 复制一条新曲线，从原位置平滑移动到补偿后的位置
  - 新曲线使用不同颜色（secondary color）
  - 动画时长：1000ms，使用FastOutSlowInEasing缓动

- **关闭等响度时**：
  - 倒放整个动画过程
  - 补偿曲线平滑移动回原位置
  - 原曲线恢复正常亮度

### 3. UI增强
- 等响度开关旁显示实时补偿值（如：+2.3dB）
- 补偿值使用醒目的secondary颜色显示
- 应用效果时显示是否启用了等响度补偿

## 代码结构

### 核心文件修改

1. **AutoEqData.kt**
   - 添加 `calculateLoudnessCompensation()` 方法
   - 添加 `getAWeightingFactor()` 私有方法
   - 添加 `withLoudnessCompensation()` 方法

2. **MainViewModel.kt**
   - 在 `MainUiState` 中添加 `isLoudnessCompensationEnabled` 状态
   - 添加 `toggleLoudnessCompensation()` 方法
   - 更新 `applyAutoEq()` 方法支持补偿

3. **EqCurveChart.kt**
   - 添加动画支持（使用 `animateFloatAsState`）
   - 实现 `drawAnimatedEqCurve()` 方法
   - 支持双曲线显示（原始+补偿）

4. **MainScreen.kt**
   - 更新 `ControlButtons` 组件
   - 添加补偿值显示UI
   - 传递等响度状态到图表组件

## 使用方法

1. 选择并导入AutoEq文件
2. 在控制面板中找到"等响度补偿"开关
3. 开启开关后，可以看到：
   - EQ曲线图中的动画效果
   - 开关旁显示的补偿值
4. 点击"应用AutoEq"时会自动应用补偿
5. 成功消息会显示是否启用了等响度补偿

## 技术细节

### A-weighting计算公式
```kotlin
val numerator = 12194.0 * 12194.0 * f4
val denominator = (f2 + 20.6 * 20.6) * 
                 sqrt((f2 + 107.7 * 107.7) * (f2 + 737.9 * 737.9)) * 
                 (f2 + 12194.0 * 12194.0)
```

### 动画插值
- 使用线性插值在原始增益和补偿增益之间过渡
- 频率保持不变，只有增益值发生变化
- 支持Catmull-Rom样条曲线平滑

### 性能优化
- 使用 `remember` 缓存计算结果
- 补偿值只在EQ数据变化时重新计算
- 动画使用Compose的优化动画系统
