package cn.ykload.flowmix.audio

import android.media.audiofx.DynamicsProcessing
import android.util.Log
import cn.ykload.flowmix.data.AutoEqData
import cn.ykload.flowmix.data.EqBand
import cn.ykload.flowmix.utils.Constants
import kotlin.math.abs


/**
 * 音频效果管理器
 * 负责管理全局音频效果，使用DynamicsProcessing API实现AutoEq
 */
class AudioEffectManager {
    
    companion object {
        private const val TAG = "AudioEffectManager"
        private const val EFFECT_PRIORITY = 0
        private const val CHANNEL_COUNT = 2 // 立体声

        // DynamicsProcessing配置常量
        private const val VARIANT = DynamicsProcessing.VARIANT_FAVOR_FREQUENCY_RESOLUTION
        private const val PRE_EQ_IN_USE = true
        private const val MBC_IN_USE = false
        private const val POST_EQ_IN_USE = false
        private const val LIMITER_IN_USE = false
    }
    
    private var dynamicsProcessing: DynamicsProcessing? = null
    private var isEffectEnabled = false
    private var currentEqData: AutoEqData? = null
    
    /**
     * 应用AutoEq配置（智能选择热更新或完全重载）
     * @param autoEqData AutoEq数据
     * @param forceFullReload 是否强制完全重载
     * @return 是否成功应用
     */
    fun applyAutoEq(autoEqData: AutoEqData, forceFullReload: Boolean = false): Boolean {
        return try {
            Log.d(TAG, "开始应用AutoEq配置: ${autoEqData.name}")
            Log.d(TAG, "频段数量: ${autoEqData.bands.size}")

            // 如果强制完全重载或者当前没有效果器，则进行完全重载
            if (forceFullReload || dynamicsProcessing == null || !isEffectEnabled) {
                Log.d(TAG, "执行完全重载 (强制: ${forceFullReload}, 无效果器: ${dynamicsProcessing == null}, 未启用: ${!isEffectEnabled})")
                return applyAutoEqFullReload(autoEqData)
            }

            // 尝试热更新
            val changedBands = detectEqDifferences(autoEqData)
            if (changedBands == null) {
                // 需要完全重载（频段数量或频率结构变化）
                Log.d(TAG, "配置结构变化，执行完全重载")
                return applyAutoEqFullReload(autoEqData)
            }

            if (changedBands.isEmpty()) {
                // 没有变化，直接更新引用
                Log.d(TAG, "配置无变化，跳过更新")
                currentEqData = autoEqData
                return true
            }

            // 执行热更新
            val hotUpdateSuccess = hotUpdateAutoEq(autoEqData, changedBands)
            if (hotUpdateSuccess) {
                Log.d(TAG, "AutoEq热更新成功")
                return true
            } else {
                // 热更新失败，回退到完全重载
                Log.w(TAG, "热更新失败，回退到完全重载")
                return applyAutoEqFullReload(autoEqData)
            }

        } catch (e: Exception) {
            Log.e(TAG, "应用AutoEq配置失败", e)
            releaseEffect()
            false
        }
    }

    /**
     * 完全重载AutoEq配置（原有的完整重载逻辑）
     * @param autoEqData AutoEq数据
     * @return 是否成功应用
     */
    private fun applyAutoEqFullReload(autoEqData: AutoEqData): Boolean {
        return try {
            Log.d(TAG, "开始完全重载AutoEq配置: ${autoEqData.name}")

            // 释放之前的效果器
            releaseEffect()

            // 创建DynamicsProcessing配置
            val config = createDynamicsProcessingConfig(autoEqData.bands)

            // 创建DynamicsProcessing实例
            dynamicsProcessing = DynamicsProcessing(
                EFFECT_PRIORITY,
                Constants.GLOBAL_AUDIO_SESSION_ID,
                config
            )

            // 启用效果器
            dynamicsProcessing?.setEnabled(true)
            isEffectEnabled = true
            currentEqData = autoEqData

            Log.d(TAG, "AutoEq完全重载成功")
            true
        } catch (e: Exception) {
            Log.e(TAG, "AutoEq完全重载失败", e)
            releaseEffect()
            false
        }
    }
    
    /**
     * 创建DynamicsProcessing配置
     */
    private fun createDynamicsProcessingConfig(bands: List<EqBand>): DynamicsProcessing.Config {
        // 计算需要的频段数量
        val bandCount = bands.size

        Log.d(TAG, "创建DynamicsProcessing配置，频段数: ${bandCount}")

        // 创建配置构建器
        val configBuilder = DynamicsProcessing.Config.Builder(
            VARIANT,
            CHANNEL_COUNT,
            PRE_EQ_IN_USE,
            bandCount, // PreEQ频段数
            MBC_IN_USE,
            0, // MBC频段数
            POST_EQ_IN_USE,
            0, // PostEQ频段数
            LIMITER_IN_USE
        )

        // 创建PreEQ配置
        val preEq = DynamicsProcessing.Eq(true, true, bandCount)

        // 配置PreEQ频段
        bands.forEachIndexed { index, band ->
            try {
                // 创建EQ频段配置
                val eqBand = DynamicsProcessing.EqBand(
                    true, // enabled
                    band.frequency, // cutoffFrequency
                    band.gain // gain
                )

                // 设置频段
                preEq.setBand(index, eqBand)

                Log.v(TAG, "配置频段 ${index}: ${band.frequency}Hz, ${band.gain}dB")
            } catch (e: Exception) {
                Log.w(TAG, "配置频段 ${index} 失败: ${e.message}")
            }
        }

        // 为每个声道设置相同的PreEQ配置
        for (channel in 0 until CHANNEL_COUNT) {
            try {
                configBuilder.setPreEqByChannelIndex(channel, preEq)
            } catch (e: Exception) {
                Log.w(TAG, "设置声道 ${channel} 的PreEQ失败: ${e.message}")
            }
        }

        return configBuilder.build()
    }
    
    /**
     * 启用/禁用音频效果
     */
    fun setEnabled(enabled: Boolean): Boolean {
        return try {
            dynamicsProcessing?.setEnabled(enabled)
            isEffectEnabled = enabled
            Log.d(TAG, "音频效果${if (enabled) "启用" else "禁用"}成功")
            true
        } catch (e: Exception) {
            Log.e(TAG, "设置音频效果状态失败", e)
            false
        }
    }
    
    /**
     * 释放音频效果资源
     */
    fun releaseEffect() {
        try {
            dynamicsProcessing?.release()
            dynamicsProcessing = null
            isEffectEnabled = false
            currentEqData = null
            Log.d(TAG, "音频效果资源已释放")
        } catch (e: Exception) {
            Log.e(TAG, "释放音频效果资源失败", e)
        }
    }
    
    /**
     * 检查是否支持DynamicsProcessing
     */
    fun isDynamicsProcessingSupported(): Boolean {
        return try {
            val testEffect = DynamicsProcessing(
                EFFECT_PRIORITY,
                Constants.GLOBAL_AUDIO_SESSION_ID,
                createMinimalConfig()
            )
            testEffect.release()
            true
        } catch (e: Exception) {
            Log.w(TAG, "设备不支持DynamicsProcessing: ${e.message}")
            false
        }
    }
    
    /**
     * 创建最小配置用于测试
     */
    private fun createMinimalConfig(): DynamicsProcessing.Config {
        return DynamicsProcessing.Config.Builder(
            VARIANT,
            CHANNEL_COUNT,
            false, // PreEQ
            0,
            false, // MBC
            0,
            false, // PostEQ
            0,
            false  // Limiter
        ).build()
    }
    
    /**
     * 获取当前效果状态
     */
    fun isEnabled(): Boolean = isEffectEnabled
    
    /**
     * 获取当前EQ数据
     */
    fun getCurrentEqData(): AutoEqData? = currentEqData

    /**
     * 检测两个AutoEq配置之间的差异
     * @param newEqData 新的AutoEq配置
     * @return 需要更新的频段索引列表，如果需要完全重载则返回null
     */
    private fun detectEqDifferences(newEqData: AutoEqData): List<Int>? {
        val currentData = currentEqData ?: return null

        // 如果频段数量不同，需要完全重载
        if (currentData.bands.size != newEqData.bands.size) {
            Log.d(TAG, "频段数量变化，需要完全重载: ${currentData.bands.size} -> ${newEqData.bands.size}")
            return null
        }

        val changedBands = mutableListOf<Int>()

        // 检查每个频段的变化
        for (i in currentData.bands.indices) {
            val currentBand = currentData.bands[i]
            val newBand = newEqData.bands[i]

            // 如果频率不同，需要完全重载（频率结构变化）
            if (currentBand.frequency != newBand.frequency) {
                Log.d(TAG, "频率结构变化，需要完全重载: 频段${i} ${currentBand.frequency}Hz -> ${newBand.frequency}Hz")
                return null
            }

            // 如果增益有变化（允许0.01dB的误差）
            if (abs(currentBand.gain - newBand.gain) > 0.01f) {
                changedBands.add(i)
                Log.v(TAG, "频段${i}增益变化: ${currentBand.gain}dB -> ${newBand.gain}dB")
            }
        }

        Log.d(TAG, "检测到${changedBands.size}个频段需要更新: ${changedBands}")
        return changedBands
    }

    /**
     * 热更新AutoEq配置（仅更新变化的频段）
     * @param newEqData 新的AutoEq配置
     * @param changedBands 需要更新的频段索引列表
     * @return 是否成功热更新
     */
    private fun hotUpdateAutoEq(newEqData: AutoEqData, changedBands: List<Int>): Boolean {
        return try {
            Log.d(TAG, "开始热更新AutoEq配置: ${newEqData.name}")
            Log.d(TAG, "更新${changedBands.size}个频段: ${changedBands}")

            val dp = dynamicsProcessing ?: run {
                Log.e(TAG, "热更新失败: DynamicsProcessing实例为null")
                return false
            }

            // 验证效果器状态
            if (!isEffectEnabled) {
                Log.e(TAG, "热更新失败: 效果器未启用")
                return false
            }

            var successCount = 0
            var failureCount = 0

            // 更新变化的频段
            for (bandIndex in changedBands) {
                if (bandIndex >= newEqData.bands.size) {
                    Log.e(TAG, "热更新失败: 频段索引超出范围 ${bandIndex} >= ${newEqData.bands.size}")
                    return false
                }

                val newBand = newEqData.bands[bandIndex]

                // 验证频段参数
                if (newBand.frequency <= 0 || newBand.gain < -30f || newBand.gain > 30f) {
                    Log.e(TAG, "热更新失败: 频段参数无效 ${bandIndex}: ${newBand.frequency}Hz, ${newBand.gain}dB")
                    return false
                }

                // 创建新的EQ频段配置
                val eqBand = DynamicsProcessing.EqBand(
                    true, // enabled
                    newBand.frequency, // cutoffFrequency
                    newBand.gain // gain
                )

                // 为每个声道更新这个频段
                var bandUpdateSuccess = true
                for (channel in 0 until CHANNEL_COUNT) {
                    try {
                        dp.setPreEqBandByChannelIndex(channel, bandIndex, eqBand)
                        Log.v(TAG, "热更新频段 ${bandIndex} (声道${channel}): ${newBand.frequency}Hz, ${newBand.gain}dB")
                    } catch (e: Exception) {
                        Log.e(TAG, "热更新频段 ${bandIndex} (声道${channel}) 失败: ${e.message}", e)
                        bandUpdateSuccess = false
                        failureCount++
                        break
                    }
                }

                if (bandUpdateSuccess) {
                    successCount++
                } else {
                    Log.e(TAG, "频段 ${bandIndex} 更新失败，终止热更新")
                    return false
                }
            }

            // 更新当前EQ数据
            currentEqData = newEqData

            Log.d(TAG, "AutoEq热更新成功: 成功更新${successCount}个频段，失败${failureCount}个")
            true
        } catch (e: Exception) {
            Log.e(TAG, "AutoEq热更新异常", e)
            false
        }
    }
}
