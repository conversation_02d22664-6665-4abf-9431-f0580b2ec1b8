package cn.ykload.flowmix.network

import cn.ykload.flowmix.data.DataSourcesResponse
import cn.ykload.flowmix.data.BrandsResponse
import cn.ykload.flowmix.data.HeadphonesResponse
import cn.ykload.flowmix.data.FrequencyDataResponse
import cn.ykload.flowmix.data.TargetCurvesResponse
import cn.ykload.flowmix.data.TargetCurveDataResponse
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Path

/**
 * 频响数据API接口
 */
interface FrequencyResponseApi {

    /**
     * 获取所有数据源
     */
    @GET("api/sources")
    suspend fun getDataSources(): Response<DataSourcesResponse>

    /**
     * 获取指定数据源的所有品牌
     */
    @GET("api/sources/{sourceName}/brands")
    suspend fun getBrands(@Path("sourceName") sourceName: String): Response<BrandsResponse>

    /**
     * 获取指定数据源和品牌下的所有耳机
     */
    @GET("api/sources/{sourceName}/brands/{brandName}/headphones")
    suspend fun getHeadphones(
        @Path("sourceName") sourceName: String,
        @Path("brandName") brandName: String
    ): Response<HeadphonesResponse>

    /**
     * 获取指定数据源、品牌和耳机的频响数据
     */
    @GET("api/sources/{sourceName}/brands/{brandName}/headphones/{headphoneName}")
    suspend fun getFrequencyData(
        @Path("sourceName") sourceName: String,
        @Path("brandName") brandName: String,
        @Path("headphoneName") headphoneName: String
    ): Response<FrequencyDataResponse>

    /**
     * 获取所有目标曲线列表
     */
    @GET("api/targets")
    suspend fun getTargetCurves(): Response<TargetCurvesResponse>

    /**
     * 获取指定目标曲线数据
     */
    @GET("api/targets/{targetName}")
    suspend fun getTargetCurveData(
        @Path("targetName") targetName: String
    ): Response<TargetCurveDataResponse>
}
