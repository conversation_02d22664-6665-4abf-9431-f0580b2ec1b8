package cn.ykload.flowmix.ui.screen

import androidx.compose.animation.*
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import cn.ykload.flowmix.permission.PermissionManager
import cn.ykload.flowmix.ui.component.BottomToast
import cn.ykload.flowmix.ui.component.BottomModalType
import cn.ykload.flowmix.ui.theme.PlaywriteAUQLDFontFamily
import cn.ykload.flowmix.viewmodel.OnboardingStep
import cn.ykload.flowmix.viewmodel.OnboardingViewModel

@OptIn(ExperimentalMaterial3Api::class, ExperimentalAnimationApi::class)
@Composable
fun OnboardingScreen(
    viewModel: OnboardingViewModel,
    permissionManager: PermissionManager,
    onComplete: () -> Unit,
    modifier: Modifier = Modifier
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    
    // 监听完成状态
    LaunchedEffect(uiState.currentStep) {
        if (uiState.currentStep == OnboardingStep.COMPLETE) {
            onComplete()
        }
    }
    
    Scaffold(
        modifier = modifier.fillMaxSize(),
        contentWindowInsets = WindowInsets(0)
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .statusBarsPadding()
                .navigationBarsPadding()
                .verticalScroll(rememberScrollState()),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 进度指示器
            OnboardingProgressIndicator(
                currentStep = uiState.currentStep,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            )
            
            // 内容区域
            Box(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth()
                    .padding(horizontal = 24.dp),
                contentAlignment = Alignment.Center
            ) {
                AnimatedContent(
                    targetState = uiState.currentStep,
                    transitionSpec = {
                        slideInHorizontally(
                            initialOffsetX = { if (targetState > initialState) 300 else -300 },
                            animationSpec = tween(300)
                        ) + fadeIn(animationSpec = tween(300)) with
                        slideOutHorizontally(
                            targetOffsetX = { if (targetState > initialState) -300 else 300 },
                            animationSpec = tween(300)
                        ) + fadeOut(animationSpec = tween(300))
                    },
                    label = "onboarding_content"
                ) { step ->
                    when (step) {
                        OnboardingStep.WELCOME -> WelcomeStep(
                            isDeviceSupported = uiState.isDeviceSupported
                        )
                        OnboardingStep.PERMISSIONS -> PermissionsStep(
                            hasAudioPermission = uiState.hasAudioPermission,
                            hasBluetoothPermission = uiState.hasBluetoothPermission,
                            hasStoragePermission = uiState.hasStoragePermission,
                            hasNotificationPermission = uiState.hasNotificationPermission,
                            isRequestingPermissions = uiState.isRequestingPermissions,
                            onRequestPermissions = viewModel::requestPermissions
                        )
                        OnboardingStep.READY -> ReadyStep()
                        OnboardingStep.COMPLETE -> Unit // 不会显示，因为会触发onComplete
                    }
                }
            }
            
            // 底部按钮 - 只保留主要按钮，移除返回和跳过按钮
            OnboardingMainButton(
                currentStep = uiState.currentStep,
                canProceed = uiState.canProceed,
                hasAllPermissions = uiState.hasAllPermissions,
                isRequestingPermissions = uiState.isRequestingPermissions,
                onNext = viewModel::nextStep,
                onRequestPermissions = viewModel::requestPermissions,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp)
            )
        }

        // 使用底部模态框显示错误消息
        BottomToast(
            isVisible = uiState.errorMessage != null,
            message = uiState.errorMessage ?: "",
            type = BottomModalType.ERROR,
            onDismiss = viewModel::clearError
        )
    }
}

@Composable
private fun OnboardingProgressIndicator(
    currentStep: OnboardingStep,
    modifier: Modifier = Modifier
) {
    val steps = listOf(
        OnboardingStep.WELCOME,
        OnboardingStep.PERMISSIONS,
        OnboardingStep.READY
    )
    
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ) {
        steps.forEachIndexed { index, step ->
            val isActive = step == currentStep
            val isCompleted = currentStep > step
            
            // 步骤圆点
            Box(
                modifier = Modifier
                    .size(if (isActive || isCompleted) 12.dp else 8.dp)
                    .background(
                        color = if (isActive || isCompleted)
                            MaterialTheme.colorScheme.primary
                        else
                            MaterialTheme.colorScheme.outline,
                        shape = CircleShape
                    )
            )
            
            // 连接线
            if (index < steps.size - 1) {
                Box(
                    modifier = Modifier
                        .width(40.dp)
                        .height(2.dp)
                        .background(
                            color = if (currentStep > step) 
                                MaterialTheme.colorScheme.primary 
                            else 
                                MaterialTheme.colorScheme.outline,
                            shape = RoundedCornerShape(1.dp)
                        )
                )
            }
        }
    }
}

@Composable
private fun WelcomeStep(
    isDeviceSupported: Boolean,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(horizontal = 16.dp)
            .verticalScroll(rememberScrollState()),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Spacer(modifier = Modifier.height(32.dp))
        // 应用图标
        Icon(
            painter = painterResource(id = cn.ykload.flowmix.R.drawable.ic_flowmix),
            contentDescription = null,
            modifier = Modifier.size(80.dp),
            tint = MaterialTheme.colorScheme.primary
        )

        Text(
            text = "Flowmix",
            style = MaterialTheme.typography.headlineMedium.copy(
                fontFamily = PlaywriteAUQLDFontFamily
            ),
            textAlign = TextAlign.Center
        )

        Text(
            text = "全局AutoEq\n看各类频响数据\n一键拟合目标线\n连耳机自动切换\n跨设备同步配置\n~ 这里全都有 ~",
            style = MaterialTheme.typography.bodyLarge,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        // 设备支持状态
        Card(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(25.dp),
            colors = CardDefaults.cardColors(
                containerColor = if (isDeviceSupported)
                    MaterialTheme.colorScheme.primaryContainer
                else
                    MaterialTheme.colorScheme.errorContainer
            )
        ) {
            Row(
                modifier = Modifier.padding(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = if (isDeviceSupported) Icons.Default.CheckCircle else Icons.Default.Error,
                    contentDescription = null,
                    tint = if (isDeviceSupported)
                        MaterialTheme.colorScheme.onPrimaryContainer
                    else
                        MaterialTheme.colorScheme.onErrorContainer
                )

                Spacer(modifier = Modifier.width(12.dp))

                Text(
                    text = if (isDeviceSupported)
                        "经检测，你的设备可以使用 Flowmix 喔~"
                    else
                        "抱歉，您的设备不兼容。如果您在使用Wavelet，请确保关闭Wavelet或重启后重新打开App",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium,
                    color = if (isDeviceSupported)
                        MaterialTheme.colorScheme.onPrimaryContainer
                    else
                        MaterialTheme.colorScheme.onErrorContainer
                )
            }
        }

        Spacer(modifier = Modifier.height(32.dp))
    }
}

@Composable
private fun PermissionsStep(
    hasAudioPermission: Boolean,
    hasBluetoothPermission: Boolean,
    hasStoragePermission: Boolean,
    hasNotificationPermission: Boolean,
    isRequestingPermissions: Boolean,
    onRequestPermissions: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(horizontal = 16.dp)
            .verticalScroll(rememberScrollState()),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Spacer(modifier = Modifier.height(32.dp))
        Icon(
            imageVector = Icons.Default.Security,
            contentDescription = null,
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.primary
        )

        Text(
            text = "需要的权限",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Center
        )

        Text(
            text = "为确保最佳体验，Flowmix 需要以下权限：",
            style = MaterialTheme.typography.bodyLarge,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        // 权限列表
        Column(
            modifier = Modifier.fillMaxWidth(),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            PermissionItem(
                icon = Icons.Default.VolumeUp,
                title = "音频设置权限",
                description = "用于应用全局 AutoEq ",
                isGranted = hasAudioPermission
            )

            PermissionItem(
                icon = Icons.Default.Bluetooth,
                title = "蓝牙权限",
                description = "用于检测蓝牙音频设备，自动切换配置",
                isGranted = hasBluetoothPermission
            )

            PermissionItem(
                icon = Icons.Default.Folder,
                title = "文件读取权限",
                description = "用于读取 AutoEq 文件",
                isGranted = hasStoragePermission
            )

            PermissionItem(
                icon = Icons.Default.Notifications,
                title = "通知权限",
                description = "用于显示 Flowmix 状态和提高后台存活率",
                isGranted = hasNotificationPermission
            )
        }

        Spacer(modifier = Modifier.height(32.dp))
    }
}

@Composable
private fun PermissionItem(
    icon: ImageVector,
    title: String,
    description: String,
    isGranted: Boolean,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(25.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (isGranted)
                MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.5f)
            else
                MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Row(
            modifier = Modifier.padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(24.dp)
            )

            Spacer(modifier = Modifier.width(16.dp))

            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            Icon(
                imageVector = if (isGranted) Icons.Default.CheckCircle else Icons.Default.RadioButtonUnchecked,
                contentDescription = null,
                tint = if (isGranted)
                    MaterialTheme.colorScheme.primary
                else
                    MaterialTheme.colorScheme.outline,
                modifier = Modifier.size(20.dp)
            )
        }
    }
}

@Composable
private fun ReadyStep(
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(horizontal = 16.dp)
            .verticalScroll(rememberScrollState()),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Spacer(modifier = Modifier.height(32.dp))

        Icon(
            imageVector = Icons.Default.Celebration,
            contentDescription = null,
            modifier = Modifier.size(80.dp),
            tint = MaterialTheme.colorScheme.primary
        )

        Text(
            text = "Let's Flow On!!!",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Center
        )

        Text(
            text = "一切准备就绪！\n尽情体验 Flowmix 便捷强大的功能吧！\n\n(Alpha 测试，若有Bug请反馈)",
            style = MaterialTheme.typography.bodyLarge,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Spacer(modifier = Modifier.height(32.dp))
    }
}

@Composable
private fun OnboardingMainButton(
    currentStep: OnboardingStep,
    canProceed: Boolean,
    hasAllPermissions: Boolean,
    isRequestingPermissions: Boolean,
    onNext: () -> Unit,
    onRequestPermissions: () -> Unit,
    modifier: Modifier = Modifier
) {
    Button(
        onClick = {
            when (currentStep) {
                OnboardingStep.PERMISSIONS -> {
                    if (hasAllPermissions) {
                        onNext()
                    } else {
                        onRequestPermissions()
                    }
                }
                else -> onNext()
            }
        },
        enabled = when (currentStep) {
            OnboardingStep.PERMISSIONS -> !isRequestingPermissions
            else -> canProceed
        },
        modifier = modifier
    ) {
        if (currentStep == OnboardingStep.PERMISSIONS && isRequestingPermissions) {
            CircularProgressIndicator(
                modifier = Modifier.size(16.dp),
                strokeWidth = 2.dp
            )
            Spacer(modifier = Modifier.width(8.dp))
        }
        Text(
            text = when (currentStep) {
                OnboardingStep.WELCOME -> "让我们开始吧"
                OnboardingStep.PERMISSIONS -> {
                    if (hasAllPermissions) "下一步" else "授予权限"
                }
                OnboardingStep.READY -> "Flowmix，启动！"
                OnboardingStep.COMPLETE -> "Now Loading..."
            }
        )
    }
}
