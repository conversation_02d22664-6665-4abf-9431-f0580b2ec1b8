@echo off
echo ========================================
echo FlowMix 优化混淆配置测试脚本
echo ========================================
echo.

echo [1/8] 清理之前的构建...
call gradlew clean
if %errorlevel% neq 0 (
    echo ✗ 清理失败
    pause
    exit /b 1
)
echo ✓ 清理完成
echo.

echo [2/8] 构建Release版本...
call gradlew assembleRelease
if %errorlevel% neq 0 (
    echo ✗ 构建失败
    pause
    exit /b 1
)
echo ✓ 构建完成
echo.

echo [3/8] 检查APK文件...
if exist "app\build\outputs\apk\release\app-release.apk" (
    echo ✓ APK文件生成成功
    for %%I in ("app\build\outputs\apk\release\app-release.apk") do echo   文件大小: %%~zI bytes
) else (
    echo ✗ APK文件未找到
    pause
    exit /b 1
)
echo.

echo [4/8] 检查混淆映射文件...
if exist "app\build\outputs\mapping\release\mapping.txt" (
    echo ✓ ProGuard mapping文件生成成功
    for /f %%i in ('find /c /v "" ^< "app\build\outputs\mapping\release\mapping.txt"') do echo   映射条目数: %%i
) else (
    echo ✗ ProGuard mapping文件未找到
    pause
    exit /b 1
)
echo.

echo [5/8] 检查关键类的混淆情况...
echo 检查WebSocket相关类:
findstr /C:"WebSocketManager" "app\build\outputs\mapping\release\mapping.txt" >nul
if %errorlevel% equ 0 (
    echo ✓ WebSocketManager在映射中找到
    findstr /C:"WebSocketManager" "app\build\outputs\mapping\release\mapping.txt"
) else (
    echo ⚠ WebSocketManager未在映射中找到（可能被完全保护）
)

findstr /C:"CloudSyncManager" "app\build\outputs\mapping\release\mapping.txt" >nul
if %errorlevel% equ 0 (
    echo ✓ CloudSyncManager在映射中找到
    findstr /C:"CloudSyncManager" "app\build\outputs\mapping\release\mapping.txt"
) else (
    echo ⚠ CloudSyncManager未在映射中找到（可能被完全保护）
)
echo.

echo [6/8] 检查数据类的混淆情况...
findstr /C:"AuthMessage" "app\build\outputs\mapping\release\mapping.txt" >nul
if %errorlevel% equ 0 (
    echo ✓ AuthMessage在映射中找到（被混淆）
) else (
    echo ⚠ AuthMessage未在映射中找到（被完全保护）
)

findstr /C:"ClientInfo" "app\build\outputs\mapping\release\mapping.txt" >nul
if %errorlevel% equ 0 (
    echo ✓ ClientInfo在映射中找到（被混淆）
) else (
    echo ⚠ ClientInfo未在映射中找到（被完全保护）
)
echo.

echo [7/8] 分析APK大小变化...
if exist "app\build\outputs\apk\release\app-release.apk" (
    for %%I in ("app\build\outputs\apk\release\app-release.apk") do (
        set /a size_mb=%%~zI/1024/1024
        echo   当前APK大小: %%~zI bytes ^(约 !size_mb! MB^)
    )
)
echo.

echo [8/8] 生成测试报告...
echo ======================================== > proguard_test_report.txt
echo FlowMix 优化混淆配置测试报告 >> proguard_test_report.txt
echo 测试时间: %date% %time% >> proguard_test_report.txt
echo ======================================== >> proguard_test_report.txt
echo. >> proguard_test_report.txt

if exist "app\build\outputs\apk\release\app-release.apk" (
    for %%I in ("app\build\outputs\apk\release\app-release.apk") do (
        echo APK大小: %%~zI bytes >> proguard_test_report.txt
    )
)

if exist "app\build\outputs\mapping\release\mapping.txt" (
    for /f %%i in ('find /c /v "" ^< "app\build\outputs\mapping\release\mapping.txt"') do (
        echo 混淆映射条目数: %%i >> proguard_test_report.txt
    )
)

echo. >> proguard_test_report.txt
echo 关键类保护状态: >> proguard_test_report.txt
findstr /C:"WebSocketManager" "app\build\outputs\mapping\release\mapping.txt" >nul
if %errorlevel% equ 0 (
    echo - WebSocketManager: 部分混淆 >> proguard_test_report.txt
) else (
    echo - WebSocketManager: 完全保护 >> proguard_test_report.txt
)

findstr /C:"AuthMessage" "app\build\outputs\mapping\release\mapping.txt" >nul
if %errorlevel% equ 0 (
    echo - AuthMessage: 部分混淆 >> proguard_test_report.txt
) else (
    echo - AuthMessage: 完全保护 >> proguard_test_report.txt
)

echo ✓ 测试报告已生成: proguard_test_report.txt
echo.

echo ========================================
echo 测试完成！
echo ========================================
echo.
echo 下一步建议:
echo 1. 安装生成的APK到设备进行功能测试
echo 2. 特别测试WebSocket连接和同步功能
echo 3. 检查日志输出确认没有序列化错误
echo 4. 如果功能正常，可以进一步优化混淆规则
echo.
pause
