# BottomNavBar 文本动画功能使用说明

## 功能描述

这个功能为 BottomNavBar 添加了一个动画效果，当用户点击主开关时会：

1. **淡出动画** (0.5秒)：BottomNavBar 上的所有导航元素淡出（主开关除外）
2. **文本进入动画** (0.75秒)：在淡出动画执行过程中（100ms后），两个文本分别在左右两侧显示
   - 使用带回弹的非线性缩放动画
   - 文本显示为描边样式，使用 Material 主题色
   - 左侧文本左对齐，右侧文本右对齐
   - 文本垂直位置与主开关中心对齐
   - 增加了字符间距 (4.sp)
3. **等待** (0.25秒)：文本完全显示后等待
4. **反向动画**：文本退出的同时导航元素淡入

## 自动触发

当用户点击 Flowmix 主开关时，会自动显示：
- 开启时：`"Flowmix"` 和 `"ON"`
- 关闭时：`"Flowmix"` 和 `"OFF"`

## 手动调用

如果需要在其他地方手动触发这个动画，可以使用：

```kotlin
// 在 MainNavigationScreen 的 Composable 中
showAnimatedTextOverlay("左侧文本", "右侧文本")

// 或者使用辅助函数
showBottomNavTextAnimation(
    leftText = "自定义左侧",
    rightText = "自定义右侧",
    showAnimatedTextOverlay = showAnimatedTextOverlay
)
```

## 动画参数

- **淡出动画时长**：500ms，使用 FastOutSlowInEasing
- **文本缩放动画**：使用 Spring 动画，带中等回弹效果
- **文本字体大小**：36sp
- **描边宽度**：1dp
- **字符间距**：4sp
- **动画延迟**：文本动画在淡出动画开始 100ms 后启动

## 注意事项

1. 动画过程中会防止重复触发
2. 文本位置会自动与主开关中心对齐
3. 反向动画时，导航元素会在文本退出过程中开始淡入，确保流畅的视觉体验
