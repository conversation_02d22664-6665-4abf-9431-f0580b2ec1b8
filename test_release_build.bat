@echo off
echo ========================================
echo FlowMix Release Build Test Script
echo ========================================
echo.

echo [1/5] Cleaning project...
call gradlew clean
if %errorlevel% neq 0 (
    echo ERROR: Clean failed
    pause
    exit /b 1
)
echo Clean completed successfully.
echo.

echo [2/5] Building release APK...
call gradlew assembleRelease
if %errorlevel% neq 0 (
    echo ERROR: Release build failed
    pause
    exit /b 1
)
echo Release build completed successfully.
echo.

echo [3/5] Checking output files...
if exist "app\build\outputs\apk\release\app-release.apk" (
    echo ✓ APK file generated successfully
) else (
    echo ✗ APK file not found
    pause
    exit /b 1
)

if exist "app\build\outputs\mapping\release\mapping.txt" (
    echo ✓ ProGuard mapping file generated
) else (
    echo ✗ ProGuard mapping file not found
)
echo.

echo [4/5] APK Information:
for %%I in ("app\build\outputs\apk\release\app-release.apk") do (
    echo File size: %%~zI bytes
    echo File path: %%~fI
)
echo.

echo [5/5] Next steps:
echo 1. Install the APK: adb install app\build\outputs\apk\release\app-release.apk
echo 2. Test data source loading functionality
echo 3. Check for the ParameterizedType error
echo.

echo ========================================
echo Build completed successfully!
echo ========================================
pause
