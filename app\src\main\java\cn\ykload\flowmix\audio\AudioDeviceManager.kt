package cn.ykload.flowmix.audio

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.media.AudioAttributes
import android.media.AudioDeviceCallback
import android.media.AudioDeviceInfo
import android.media.AudioManager
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.annotation.RequiresApi
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 音频设备类型枚举
 */
enum class AudioDeviceType(val displayName: String, val description: String) {
    BUILTIN_SPEAKER("内置扬声器", "设备内置扬声器"),
    WIRED_HEADPHONES("有线耳机", "3.5mm有线耳机"),
    WIRED_HEADSET("有线耳麦", "3.5mm有线耳麦"),
    BLUETOOTH_A2DP("蓝牙耳机", "蓝牙A2DP音频设备"),
    BLUETOOTH_SCO("蓝牙通话", "蓝牙SCO通话设备"),
    USB_DEVICE("USB音频", "USB音频设备"),
    USB_HEADSET("USB耳麦", "USB耳麦设备"),
    HDMI("HDMI", "HDMI音频输出"),
    UNKNOWN("未知设备", "未识别的音频设备")
}

/**
 * 音频设备信息
 */
data class FlowMixAudioDeviceInfo(
    val type: AudioDeviceType,
    val name: String,
    val id: Int,
    val isSource: Boolean = false,
    val isSink: Boolean = true,
    val sampleRates: IntArray = intArrayOf(),
    val channelMasks: IntArray = intArrayOf(),
    val encodings: IntArray = intArrayOf()
) {
    /**
     * 生成设备的唯一标识符
     */
    fun getDeviceIdentifier(): String {
        return "${type.name}_${name.hashCode()}"
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as FlowMixAudioDeviceInfo

        if (type != other.type) return false
        if (name != other.name) return false
        if (id != other.id) return false

        return true
    }

    override fun hashCode(): Int {
        var result = type.hashCode()
        result = 31 * result + name.hashCode()
        result = 31 * result + id
        return result
    }
}

/**
 * 音频设备管理器
 * 负责检测当前音频输出设备和监听设备变更
 */
class AudioDeviceManager(private val context: Context) {

    companion object {
        private const val TAG = "AudioDeviceManager"
    }

    private val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager

    // 当前音频设备状态
    private val _currentAudioDevice = MutableStateFlow<FlowMixAudioDeviceInfo?>(null)
    val currentAudioDevice: StateFlow<FlowMixAudioDeviceInfo?> = _currentAudioDevice.asStateFlow()

    // 所有可用的音频输出设备
    private val _availableAudioDevices = MutableStateFlow<List<FlowMixAudioDeviceInfo>>(emptyList())
    val availableAudioDevices: StateFlow<List<FlowMixAudioDeviceInfo>> = _availableAudioDevices.asStateFlow()

    // 设备变更监听器
    private var audioDeviceCallback: AudioDeviceCallback? = null
    private var headsetReceiver: BroadcastReceiver? = null
    private val mainHandler = Handler(Looper.getMainLooper())

    // 监听状态
    private var isMonitoringActive = false

    /**
     * 开始监听音频设备变更
     */
    fun startMonitoring() {
        Log.d(TAG, "开始监听音频设备变更")

        if (isMonitoringActive) {
            Log.d(TAG, "设备监听已经在运行中")
            return
        }

        // 初始检测当前设备
        detectCurrentAudioDevice()

        // 注册设备变更回调（API 23+）
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            try {
                registerDeviceCallback()
            } catch (e: Exception) {
                Log.w(TAG, "注册设备回调失败，将使用广播接收器", e)
            }
        }

        // 注册耳机插拔广播接收器（兼容旧版本）
        registerHeadsetReceiver()

        isMonitoringActive = true
    }

    /**
     * 停止监听音频设备变更
     */
    fun stopMonitoring() {
        Log.d(TAG, "停止监听音频设备变更")

        if (!isMonitoringActive) {
            Log.d(TAG, "设备监听已经停止")
            return
        }

        // 取消注册设备变更回调
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            unregisterDeviceCallback()
        }

        // 取消注册耳机插拔广播接收器
        unregisterHeadsetReceiver()

        isMonitoringActive = false
    }

    /**
     * 手动刷新音频设备检测
     * 在某些情况下，系统可能不会自动触发设备变更回调，可以手动调用此方法
     */
    fun refreshDeviceDetection() {
        Log.d(TAG, "手动刷新音频设备检测")
        detectCurrentAudioDevice()
    }

    /**
     * 检查是否正在监听设备变更
     */
    fun isMonitoring(): Boolean {
        return isMonitoringActive
    }

    /**
     * 获取当前音频状态的详细信息（用于调试）
     */
    fun getCurrentAudioStateInfo(): String {
        return buildString {
            appendLine("=== 音频设备状态信息 ===")
            appendLine("有线耳机: ${audioManager.isWiredHeadsetOn}")
            appendLine("蓝牙A2DP: ${audioManager.isBluetoothA2dpOn}")
            appendLine("蓝牙SCO: ${audioManager.isBluetoothScoOn}")
            appendLine("扬声器电话: ${audioManager.isSpeakerphoneOn}")
            appendLine("音频模式: ${getAudioModeString(audioManager.mode)}")

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                try {
                    val communicationDevice = audioManager.communicationDevice
                    appendLine("通信设备: ${communicationDevice?.productName ?: "无"}")

                    // 添加媒体音频优选设备信息（Android 13+）
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                        val audioAttributes = AudioAttributes.Builder()
                            .setUsage(AudioAttributes.USAGE_MEDIA)
                            .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                            .build()
                        val mediaDevices = audioManager.getAudioDevicesForAttributes(audioAttributes)
                        if (mediaDevices.isNotEmpty()) {
                            appendLine("媒体音频优选设备: ${getDeviceName(mediaDevices.first())}")
                            appendLine("媒体音频设备列表 (${mediaDevices.size}个):")
                            mediaDevices.forEachIndexed { index, device ->
                                appendLine("  [$index] ${getDeviceName(device)} (类型: ${device.type}, ID: ${device.id})")
                            }
                        } else {
                            appendLine("媒体音频优选设备: 无")
                        }
                    } else {
                        appendLine("媒体音频优选设备: 需要Android 13+")
                    }
                } catch (e: Exception) {
                    appendLine("通信设备/媒体设备: 获取失败")
                }
            }

            appendLine("\n当前检测到的设备:")
            val currentDevice = _currentAudioDevice.value
            if (currentDevice != null) {
                appendLine("- ${currentDevice.name} (${currentDevice.type.displayName})")
            } else {
                appendLine("- 无")
            }

            appendLine("\n所有可用设备:")
            _availableAudioDevices.value.forEach { device ->
                appendLine("- ${device.name} (${device.type.displayName}, ID: ${device.id})")
            }

            appendLine("========================")
        }
    }

    /**
     * 检测当前音频输出设备
     */
    private fun detectCurrentAudioDevice() {
        try {
            // 记录当前音频状态用于调试
            logCurrentAudioState()

            val devices = getAllAudioOutputDevices()
            _availableAudioDevices.value = devices

            // 确定当前活跃的音频设备
            val currentDevice = determineCurrentActiveDevice(devices)

            // 只有当设备真正发生变化时才更新状态
            val previousDevice = _currentAudioDevice.value
            if (currentDevice != previousDevice) {
                _currentAudioDevice.value = currentDevice

                Log.i(TAG, "音频设备变更: ${previousDevice?.name} -> ${currentDevice?.name}")
                Log.d(TAG, "当前音频设备: ${currentDevice?.name} (${currentDevice?.type?.displayName})")
            }

            Log.v(TAG, "可用设备数量: ${devices.size}")
            devices.forEach { device ->
                Log.v(TAG, "  - ${device.name} (${device.type.displayName}, ID: ${device.id})")
            }
        } catch (e: Exception) {
            Log.e(TAG, "检测音频设备失败", e)
        }
    }

    /**
     * 记录当前音频状态用于调试
     */
    private fun logCurrentAudioState() {
        try {
            Log.d(TAG, "=== 当前音频状态 ===")
            Log.d(TAG, "有线耳机: ${audioManager.isWiredHeadsetOn}")
            Log.d(TAG, "蓝牙A2DP: ${audioManager.isBluetoothA2dpOn}")
            Log.d(TAG, "蓝牙SCO: ${audioManager.isBluetoothScoOn}")
            Log.d(TAG, "扬声器电话: ${audioManager.isSpeakerphoneOn}")
            Log.d(TAG, "音频模式: ${getAudioModeString(audioManager.mode)}")

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                try {
                    val communicationDevice = audioManager.communicationDevice
                    Log.d(TAG, "通信设备: ${communicationDevice?.productName ?: "无"} (ID: ${communicationDevice?.id ?: "无"})")

                    // 记录媒体音频优选设备（Android 13+）
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                        val audioAttributes = AudioAttributes.Builder()
                            .setUsage(AudioAttributes.USAGE_MEDIA)
                            .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                            .build()
                        val mediaDevices = audioManager.getAudioDevicesForAttributes(audioAttributes)
                        if (mediaDevices.isNotEmpty()) {
                            Log.d(TAG, "媒体音频优选设备: ${getDeviceName(mediaDevices.first())} (ID: ${mediaDevices.first().id})")
                        } else {
                            Log.d(TAG, "媒体音频优选设备: 无")
                        }
                    }
                } catch (e: Exception) {
                    Log.d(TAG, "通信设备/媒体设备: 获取失败")
                }
            }

            // 记录所有输出设备的详细信息
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                try {
                    val outputDevices = audioManager.getDevices(AudioManager.GET_DEVICES_OUTPUTS)
                    Log.d(TAG, "所有输出设备 (${outputDevices.size}个):")
                    outputDevices.forEach { device ->
                        Log.d(TAG, "  - ${getDeviceName(device)} (类型: ${device.type}, ID: ${device.id}, 输出: ${device.isSink})")
                    }
                } catch (e: Exception) {
                    Log.w(TAG, "获取输出设备列表失败: ${e.message}")
                }
            }

            Log.d(TAG, "==================")
        } catch (e: Exception) {
            Log.w(TAG, "记录音频状态失败: ${e.message}")
        }
    }

    /**
     * 获取音频模式字符串
     */
    private fun getAudioModeString(mode: Int): String {
        return when (mode) {
            AudioManager.MODE_NORMAL -> "NORMAL"
            AudioManager.MODE_RINGTONE -> "RINGTONE"
            AudioManager.MODE_IN_CALL -> "IN_CALL"
            AudioManager.MODE_IN_COMMUNICATION -> "IN_COMMUNICATION"
            else -> "UNKNOWN($mode)"
        }
    }

    /**
     * 获取所有音频输出设备
     */
    private fun getAllAudioOutputDevices(): List<FlowMixAudioDeviceInfo> {
        val devices = mutableListOf<FlowMixAudioDeviceInfo>()
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // API 23+ 使用 getDevices 方法
            val audioDevices = audioManager.getDevices(AudioManager.GET_DEVICES_OUTPUTS)

            for (device in audioDevices) {
                val deviceType = mapAndroidAudioDeviceType(device.type)
                val deviceInfo = FlowMixAudioDeviceInfo(
                    type = deviceType,
                    name = getDeviceName(device),
                    id = device.id,
                    isSink = device.isSink,
                    isSource = device.isSource,
                    sampleRates = device.sampleRates,
                    channelMasks = device.channelMasks,
                    encodings = device.encodings
                )
                devices.add(deviceInfo)

                Log.v(TAG, "发现设备: ${deviceInfo.name} (${deviceInfo.type.displayName}, 原始类型: ${device.type}, ID: ${device.id})")
            }
        } else {
            // API 23以下的兼容处理
            devices.addAll(getLegacyAudioDevices())
        }
        
        return devices
    }

    /**
     * 映射Android音频设备类型到自定义枚举
     */
    private fun mapAndroidAudioDeviceType(androidType: Int): AudioDeviceType {
        return when (androidType) {
            AudioDeviceInfo.TYPE_BUILTIN_SPEAKER -> AudioDeviceType.BUILTIN_SPEAKER
            AudioDeviceInfo.TYPE_WIRED_HEADPHONES -> AudioDeviceType.WIRED_HEADPHONES
            AudioDeviceInfo.TYPE_WIRED_HEADSET -> AudioDeviceType.WIRED_HEADSET
            AudioDeviceInfo.TYPE_BLUETOOTH_A2DP -> AudioDeviceType.BLUETOOTH_A2DP
            AudioDeviceInfo.TYPE_BLUETOOTH_SCO -> AudioDeviceType.BLUETOOTH_SCO
            AudioDeviceInfo.TYPE_USB_DEVICE -> AudioDeviceType.USB_DEVICE
            AudioDeviceInfo.TYPE_USB_HEADSET -> AudioDeviceType.USB_HEADSET
            AudioDeviceInfo.TYPE_HDMI -> AudioDeviceType.HDMI
            // 添加更多设备类型支持
            AudioDeviceInfo.TYPE_BUILTIN_EARPIECE -> AudioDeviceType.BUILTIN_SPEAKER // 听筒当作扬声器处理
            AudioDeviceInfo.TYPE_AUX_LINE -> AudioDeviceType.WIRED_HEADPHONES // AUX线路当作有线耳机
            AudioDeviceInfo.TYPE_DOCK -> AudioDeviceType.USB_DEVICE // 扩展坞当作USB设备
            AudioDeviceInfo.TYPE_FM -> AudioDeviceType.UNKNOWN // FM收音机
            AudioDeviceInfo.TYPE_BUILTIN_MIC -> AudioDeviceType.UNKNOWN // 内置麦克风（输入设备）
            AudioDeviceInfo.TYPE_FM_TUNER -> AudioDeviceType.UNKNOWN // FM调谐器
            AudioDeviceInfo.TYPE_TV_TUNER -> AudioDeviceType.HDMI // TV调谐器当作HDMI
            AudioDeviceInfo.TYPE_TELEPHONY -> AudioDeviceType.BLUETOOTH_SCO // 电话当作蓝牙SCO
            AudioDeviceInfo.TYPE_UNKNOWN -> AudioDeviceType.UNKNOWN
            else -> {
                Log.w(TAG, "未知的音频设备类型: $androidType")
                AudioDeviceType.UNKNOWN
            }
        }
    }

    /**
     * 获取设备名称
     */
    private fun getDeviceName(device: android.media.AudioDeviceInfo): String {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            val productName = device.productName?.toString()
            if (!productName.isNullOrBlank() && productName != "Unknown") {
                productName
            } else {
                // 如果产品名称为空或未知，使用设备类型名称
                getDeviceTypeDisplayName(device.type)
            }
        } else {
            getDeviceTypeDisplayName(device.type)
        }
    }

    /**
     * 获取设备类型的显示名称
     */
    private fun getDeviceTypeDisplayName(deviceType: Int): String {
        return when (deviceType) {
            AudioDeviceInfo.TYPE_BUILTIN_SPEAKER -> "内置扬声器"
            AudioDeviceInfo.TYPE_BUILTIN_EARPIECE -> "听筒"
            AudioDeviceInfo.TYPE_WIRED_HEADPHONES -> "有线耳机"
            AudioDeviceInfo.TYPE_WIRED_HEADSET -> "有线耳麦"
            AudioDeviceInfo.TYPE_BLUETOOTH_A2DP -> "蓝牙耳机"
            AudioDeviceInfo.TYPE_BLUETOOTH_SCO -> "蓝牙通话"
            AudioDeviceInfo.TYPE_USB_DEVICE -> "USB音频设备"
            AudioDeviceInfo.TYPE_USB_HEADSET -> "USB耳麦"
            AudioDeviceInfo.TYPE_HDMI -> "HDMI音频"
            AudioDeviceInfo.TYPE_AUX_LINE -> "AUX线路"
            AudioDeviceInfo.TYPE_DOCK -> "扩展坞"
            AudioDeviceInfo.TYPE_FM -> "FM收音机"
            AudioDeviceInfo.TYPE_FM_TUNER -> "FM调谐器"
            AudioDeviceInfo.TYPE_TV_TUNER -> "TV调谐器"
            AudioDeviceInfo.TYPE_TELEPHONY -> "电话"
            AudioDeviceInfo.TYPE_UNKNOWN -> "未知设备"
            else -> "音频设备 (类型: $deviceType)"
        }
    }

    /**
     * 确定当前活跃的音频设备
     * Android 13+优先使用媒体音频优选设备列表，Android 12使用通信设备，其他版本使用设备优先级判断
     */
    private fun determineCurrentActiveDevice(devices: List<FlowMixAudioDeviceInfo>): FlowMixAudioDeviceInfo? {
        // Android 13+ 优先使用媒体音频优选设备列表
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            try {
                val mediaDevice = getMediaOutputDevice(devices)
                if (mediaDevice != null) {
                    Log.d(TAG, "通过媒体音频API检测到当前设备: ${mediaDevice.name} (${mediaDevice.type.displayName})")
                    return mediaDevice
                }
            } catch (e: Exception) {
                Log.w(TAG, "获取媒体音频设备失败，使用优先级判断: ${e.message}")
            }
        }

        // Android 12 使用通信设备作为参考
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            try {
                val communicationDevice = audioManager.communicationDevice
                if (communicationDevice != null) {
                    val activeDevice = devices.find { it.id == communicationDevice.id }
                    if (activeDevice != null) {
                        Log.d(TAG, "通过通信设备检测到当前设备: ${activeDevice.name} (${activeDevice.type.displayName})")
                        return activeDevice
                    }
                }
            } catch (e: Exception) {
                Log.w(TAG, "获取通信设备失败: ${e.message}")
            }
        }

        // 后备方案：使用优先级顺序选择设备
        val priorityOrder = listOf(
            AudioDeviceType.WIRED_HEADPHONES,
            AudioDeviceType.WIRED_HEADSET,
            AudioDeviceType.USB_HEADSET,
            AudioDeviceType.USB_DEVICE,
            AudioDeviceType.BLUETOOTH_A2DP,
            AudioDeviceType.BLUETOOTH_SCO,
            AudioDeviceType.HDMI,
            AudioDeviceType.BUILTIN_SPEAKER
        )

        for (priority in priorityOrder) {
            val device = devices.find { it.type == priority }
            if (device != null) {
                Log.d(TAG, "根据优先级选择设备: ${device.name} (${device.type.displayName})")
                return device
            }
        }

        return devices.firstOrNull()
    }

    /**
     * 获取媒体音频的优选设备（Android 13+）
     */
    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    private fun getMediaOutputDevice(devices: List<FlowMixAudioDeviceInfo>): FlowMixAudioDeviceInfo? {
        try {
            // 创建媒体音频属性
            val audioAttributes = AudioAttributes.Builder()
                .setUsage(AudioAttributes.USAGE_MEDIA)
                .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                .build()

            // 获取媒体音频的优选设备列表
            val mediaDevices = audioManager.getAudioDevicesForAttributes(audioAttributes)

            Log.d(TAG, "媒体音频优选设备列表 (${mediaDevices.size}个):")
            mediaDevices.forEachIndexed { index, device ->
                Log.d(TAG, "  [$index] ${getDeviceName(device)} (类型: ${device.type}, ID: ${device.id})")
            }

            // 列表中的第一个设备通常就是当前正在使用的设备
            if (mediaDevices.isNotEmpty()) {
                val preferredDevice = mediaDevices.first()

                // 在我们的设备列表中找到匹配的设备
                val matchingDevice = devices.find { it.id == preferredDevice.id }
                if (matchingDevice != null) {
                    Log.d(TAG, "找到匹配的媒体音频设备: ${matchingDevice.name}")
                    return matchingDevice
                } else {
                    Log.w(TAG, "媒体音频优选设备不在可用设备列表中: ${getDeviceName(preferredDevice)}")
                }
            } else {
                Log.w(TAG, "媒体音频优选设备列表为空")
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取媒体音频优选设备失败", e)
        }

        return null
    }


    /**
     * 获取旧版本API的音频设备信息
     */
    private fun getLegacyAudioDevices(): List<FlowMixAudioDeviceInfo> {
        val devices = mutableListOf<FlowMixAudioDeviceInfo>()

        // 检查有线耳机
        if (audioManager.isWiredHeadsetOn) {
            devices.add(FlowMixAudioDeviceInfo(
                type = AudioDeviceType.WIRED_HEADPHONES,
                name = "有线耳机",
                id = 1
            ))
        }

        // 检查蓝牙设备
        if (audioManager.isBluetoothA2dpOn) {
            devices.add(FlowMixAudioDeviceInfo(
                type = AudioDeviceType.BLUETOOTH_A2DP,
                name = "蓝牙音频设备",
                id = 2
            ))
        }

        // 总是包含内置扬声器
        devices.add(FlowMixAudioDeviceInfo(
            type = AudioDeviceType.BUILTIN_SPEAKER,
            name = "内置扬声器",
            id = 0
        ))

        return devices
    }





    /**
     * 注册耳机插拔广播接收器
     */
    private fun registerHeadsetReceiver() {
        headsetReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                when (intent?.action) {
                    Intent.ACTION_HEADSET_PLUG -> {
                        val state = intent.getIntExtra("state", -1)
                        Log.d(TAG, "耳机插拔事件: state=$state")
                        detectCurrentAudioDevice()
                    }
                    AudioManager.ACTION_AUDIO_BECOMING_NOISY -> {
                        Log.d(TAG, "音频变为嘈杂（可能是耳机拔出）")
                        detectCurrentAudioDevice()
                    }
                }
            }
        }
        
        val filter = IntentFilter().apply {
            addAction(Intent.ACTION_HEADSET_PLUG)
            addAction(AudioManager.ACTION_AUDIO_BECOMING_NOISY)
        }
        
        context.registerReceiver(headsetReceiver, filter)
    }

    /**
     * 取消注册耳机插拔广播接收器
     */
    private fun unregisterHeadsetReceiver() {
        headsetReceiver?.let { receiver ->
            try {
                context.unregisterReceiver(receiver)
            } catch (e: Exception) {
                Log.w(TAG, "取消注册广播接收器失败", e)
            }
            headsetReceiver = null
        }
    }

    /**
     * 注册设备变更回调（API 23+）
     */
    @RequiresApi(Build.VERSION_CODES.M)
    private fun registerDeviceCallback() {
        try {
            // 创建AudioDeviceCallback实例
            val callback = object : AudioDeviceCallback() {
                override fun onAudioDevicesAdded(addedDevices: Array<out AudioDeviceInfo>) {
                    Log.i(TAG, "音频设备已添加: ${addedDevices.size}个设备")

                    // 记录添加的设备详细信息
                    addedDevices.forEach { device ->
                        val deviceName = getDeviceName(device)
                        val deviceType = mapAndroidAudioDeviceType(device.type)
                        Log.i(TAG, "添加设备: $deviceName (类型: ${deviceType.displayName}, ID: ${device.id}, 输出: ${device.isSink})")

                        // 如果是输出设备，记录更多信息
                        if (device.isSink) {
                            Log.d(TAG, "  - 采样率: ${device.sampleRates?.joinToString() ?: "未知"}")
                            Log.d(TAG, "  - 声道: ${device.channelMasks?.joinToString() ?: "未知"}")
                            Log.d(TAG, "  - 编码: ${device.encodings?.joinToString() ?: "未知"}")
                        }
                    }

                    // 重新检测当前设备
                    detectCurrentAudioDevice()
                }

                override fun onAudioDevicesRemoved(removedDevices: Array<out AudioDeviceInfo>) {
                    Log.i(TAG, "音频设备已移除: ${removedDevices.size}个设备")

                    // 记录移除的设备详细信息
                    removedDevices.forEach { device ->
                        val deviceName = getDeviceName(device)
                        val deviceType = mapAndroidAudioDeviceType(device.type)
                        Log.i(TAG, "移除设备: $deviceName (类型: ${deviceType.displayName}, ID: ${device.id})")
                    }

                    // 重新检测当前设备
                    detectCurrentAudioDevice()
                }
            }

            audioDeviceCallback = callback

            // 注册AudioDeviceCallback
            audioManager.registerAudioDeviceCallback(callback, mainHandler)

            Log.d(TAG, "AudioDeviceCallback注册成功")
        } catch (e: Exception) {
            Log.w(TAG, "注册AudioDeviceCallback失败: ${e.message}")
            throw e
        }
    }

    /**
     * 取消注册设备变更回调（API 23+）
     */
    @RequiresApi(Build.VERSION_CODES.M)
    private fun unregisterDeviceCallback() {
        try {
            audioDeviceCallback?.let { callback ->
                audioManager.unregisterAudioDeviceCallback(callback)
                audioDeviceCallback = null
                Log.d(TAG, "AudioDeviceCallback取消注册成功")
            }
        } catch (e: Exception) {
            Log.w(TAG, "取消注册AudioDeviceCallback失败: ${e.message}")
        }
    }
}
