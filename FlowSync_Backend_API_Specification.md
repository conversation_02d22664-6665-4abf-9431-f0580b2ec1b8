# FlowSync 跨设备同步功能 - 后端开发说明

## 概述

FlowSync 是 Flowmix 应用的核心功能，用于跨设备同步音频设备配置。本文档详细说明了后端需要实现的 API 接口和 WebSocket 协议，以支持用户在多个设备间无缝同步音频设备的 AutoEq 和频响配置。

## 功能需求

### 1. 用户认证系统
- 支持基于 6 位数字登录码的认证机制
- 生成和管理长期有效的 authToken
- 与 QQ Bot 集成（由前端处理）

### 2. 实时数据同步
- 基于 WebSocket 的实时双向通信
- 云端配置数据的存储和管理
- 设备间配置数据的实时同步

### 3. 数据管理
- 用户设备配置的云端存储
- 配置数据的版本控制和冲突解决
- 数据的增量同步机制

## 数据结构

### 设备配置数据结构

```json
{
  "deviceId": "string",           // 设备唯一标识符
  "deviceName": "string",         // 设备名称
  "deviceType": "string",         // 设备类型 (WIRED_HEADPHONES, BLUETOOTH_A2DP, USB_DEVICE, etc.)
  "autoEqConfig": {               // AutoEq 配置 (可选)
    "name": "string",             // 配置名称
    "bands": [                    // EQ 频段配置
      {
        "frequency": 32.0,        // 频率 (Hz)
        "gain": -2.5              // 增益 (dB)
      }
    ],
    "isLoudnessCompensationEnabled": false,  // 等响度补偿开关
    "globalGain": 0.0             // 整体增益 (dB)
  },
  "frequencyResponseConfig": {    // 频响配置 (可选)
    "dataSource": "string",       // 数据源
    "brand": "string",            // 品牌
    "headphone": "string",        // 耳机型号
    "measurementCondition": "string",  // 测量条件
    "targetCurve": "string"       // 目标曲线
  },
  "lastUpdated": 1704067200000    // 最后更新时间戳 (毫秒)
}
```

### 用户配置集合数据结构

```json
{
  "userId": "string",             // 用户唯一标识
  "configs": {                    // 设备配置映射
    "deviceId1": { /* DeviceConfig */ },
    "deviceId2": { /* DeviceConfig */ }
  },
  "version": 1,                   // 数据版本号
  "lastUpdated": 1704067200000    // 最后更新时间戳
}
```

## API 接口规范

### 1. 用户登录接口

**POST** `https://flowsync.ykload.com/api/login`

#### 请求参数
```json
{
  "token": "Flowmix.FlowSync777-AkkoAndYKload233",  // 固定登录令牌
  "loginCode": "123456"                             // 6位数字登录码
}
```

#### 响应

**成功 (200)**
```json
{
  "success": true,
  "authToken": "c3634d71-4556-442b-b333-d6e2b8eb9ef7-ba0a8214fbf886c00977d092b6591c9e",  // 永久有效的认证令牌
  "qq": "2659368533"                                         // 用户QQ号
}
```

**失败 (403)**
```json
{
  "success": false,
  "error": "INVALID_LOGIN_CODE",
  "message": "登录码无效或已过期"
}
```

**其他错误 (400/500)**
```json
{
  "success": false,
  "error": "ERROR_CODE",
  "message": "错误描述"
}
```

## WebSocket 协议规范

### 连接地址
`wss://flowsync.ykload.com/ws`

### 1. 连接认证

客户端连接后必须立即发送认证消息：

```json
{
  "type": "auth",
  "authToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "clientInfo": {
    "platform": "android",
    "version": "1.0.0",
    "deviceId": "android_device_123"
  }
}
```

#### 认证成功响应
```json
{
  "type": "auth_success",
  "qq": "2659368533",
  "message": "认证成功"
}
```

#### 认证失败响应
```json
{
  "type": "auth_failed",
  "error": "INVALID_TOKEN",
  "message": "认证令牌无效"
}
```

### 2. 获取云端配置数据

认证成功后，客户端请求云端配置：

```json
{
  "type": "get_cloud_config"
}
```

#### 服务端响应
```json
{
  "type": "cloud_config",
  "data": {
    "qq": "2659368533",
    "configs": {
      "device_id_1": { /* DeviceConfig */ },
      "device_id_2": { /* DeviceConfig */ }
    },
    "version": 5,
    "lastUpdated": 1704067200000
  }
}
```

### 3. 同步本地配置到云端

客户端将本地配置同步到云端：

```json
{
  "type": "sync_to_cloud",
  "data": {
    "qq": "2659368533",
    "configs": {
      "device_id_1": { /* DeviceConfig */ },
      "device_id_2": { /* DeviceConfig */ }
    },
    "lastUpdated": 1704067300000
  },
  "deviceId": "android_device_123"
}
```

**重要变更**：
- **移除了 `version` 字段**：不再使用版本号进行同步控制
- **仅使用 `lastUpdated` 时间戳**：基于时间戳判断配置新旧
- **`deviceId` 字段**：标识发送更新的设备

**后端处理逻辑**：
1. 比较 `lastUpdated` 时间戳，只有更新的配置才保存
2. 将配置保存到云端
3. 向**除发送者外**的所有在线设备广播 `config_updated` 消息
4. 不要将更新消息发送回给发送者，避免循环同步

#### 服务端响应
```json
{
  "type": "sync_success",
  "syncedAt": 1704067350000,
  "message": "同步成功"
}
```

**变更**：
- **移除了 `newVersion` 字段**：不再返回版本号
- **添加了 `syncedAt` 字段**：返回服务器同步时间戳

### 4. 实时配置更新通知

当用户在其他设备上更新配置时，服务端主动推送：

```json
{
  "type": "config_updated",
  "data": {
    "deviceId": "device_id_1",
    "config": { /* DeviceConfig */ },
    "version": 7,
    "updatedBy": "android_device_456"
  }
}
```

### 5. 心跳保持连接

```json
{
  "type": "ping"
}
```

```json
{
  "type": "pong"
}
```

## 消息广播机制

### 关键问题：后加入设备无法同步到先加入设备

**问题描述**：
- 设备A先连接WebSocket
- 设备B后连接WebSocket
- 设备B的配置更新能同步到设备A ✅
- 设备A的配置更新无法同步到设备B ❌

**根本原因**：
后端的消息广播逻辑可能有问题，没有正确向所有在线设备广播配置更新。

**解决方案**：

#### 1. 设备连接管理
```javascript
// 后端需要维护用户的所有活跃连接
const userConnections = new Map(); // qq -> Set<WebSocket>

// 用户连接时
function onUserConnect(qq, websocket, deviceId) {
    if (!userConnections.has(qq)) {
        userConnections.set(qq, new Set());
    }
    userConnections.get(qq).add({
        websocket: websocket,
        deviceId: deviceId,
        connectedAt: Date.now()
    });
}

// 用户断开时
function onUserDisconnect(qq, websocket) {
    if (userConnections.has(qq)) {
        const connections = userConnections.get(qq);
        connections.forEach(conn => {
            if (conn.websocket === websocket) {
                connections.delete(conn);
            }
        });
        if (connections.size === 0) {
            userConnections.delete(qq);
        }
    }
}
```

#### 2. 消息广播逻辑
```javascript
// 处理配置同步请求
function handleSyncToCloud(qq, deviceId, configData, senderWebSocket) {
    // 1. 保存配置到数据库
    await saveConfigToDatabase(qq, configData);

    // 2. 向发送者确认同步成功
    senderWebSocket.send(JSON.stringify({
        type: "sync_success",
        newVersion: configData.version,
        message: "同步成功"
    }));

    // 3. 向该用户的所有其他设备广播更新
    const userConns = userConnections.get(qq);
    if (userConns) {
        userConns.forEach(conn => {
            // 关键：不要发送给发送者自己
            if (conn.websocket !== senderWebSocket && conn.deviceId !== deviceId) {
                conn.websocket.send(JSON.stringify({
                    type: "config_updated",
                    data: {
                        deviceId: deviceId,
                        config: configData,
                        version: configData.version,
                        updatedBy: deviceId
                    }
                }));
            }
        });
    }
}
```

#### 3. 连接状态检查
```javascript
// 定期清理无效连接
setInterval(() => {
    userConnections.forEach((connections, qq) => {
        connections.forEach(conn => {
            if (conn.websocket.readyState !== WebSocket.OPEN) {
                connections.delete(conn);
            }
        });
        if (connections.size === 0) {
            userConnections.delete(qq);
        }
    });
}, 30000); // 每30秒检查一次
```

## 技术实现要求

### 1. 数据存储
- 使用关系型数据库（如 PostgreSQL）存储用户和配置数据
- 实现数据版本控制，支持并发更新检测
- 配置 Redis 缓存提高查询性能

### 2. WebSocket 管理
- 支持多客户端同时连接
- 实现连接状态管理和自动重连机制
- 处理网络异常和连接断开情况
- **关键**：正确实现消息广播，确保所有设备都能收到更新

### 3. 安全性
- authToken 使用 JWT 格式，包含用户信息和过期时间
- 实现 token 刷新机制
- 对敏感数据进行加密存储

### 4. 性能优化
- 实现增量同步，只传输变更的配置数据
- 使用消息队列处理高并发同步请求
- 配置适当的连接池和超时设置

### 5. 错误处理
- 完善的错误码定义和错误消息
- 异常情况的日志记录
- 优雅的降级处理机制

## 部署要求

### 1. 服务器配置
- 支持 WebSocket 长连接
- 配置 SSL/TLS 证书
- 负载均衡和高可用部署

### 2. 监控和日志
- 实时监控 WebSocket 连接数和消息处理量
- 记录用户操作和系统异常日志
- 配置告警机制

## 测试建议

### 1. 单元测试
- API 接口的各种输入输出场景
- WebSocket 消息处理逻辑
- 数据同步和冲突解决机制

### 2. 集成测试
- 多客户端并发连接测试
- 网络异常恢复测试
- 大数据量同步性能测试

### 3. 压力测试
- 高并发连接压力测试
- 大量数据同步的性能测试
- 长时间运行的稳定性测试

## 数据库设计建议

### 用户表 (users)
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(50) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);
```

### 认证令牌表 (auth_tokens)
```sql
CREATE TABLE auth_tokens (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(50) REFERENCES users(user_id),
    token_hash VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_revoked BOOLEAN DEFAULT false
);
```

### 设备配置表 (device_configs)
```sql
CREATE TABLE device_configs (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(50) REFERENCES users(user_id),
    device_id VARCHAR(100) NOT NULL,
    device_name VARCHAR(255) NOT NULL,
    device_type VARCHAR(50) NOT NULL,
    config_data JSONB NOT NULL,
    version INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, device_id)
);
```

### 同步日志表 (sync_logs)
```sql
CREATE TABLE sync_logs (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(50) REFERENCES users(user_id),
    device_id VARCHAR(100),
    action VARCHAR(20) NOT NULL, -- 'CREATE', 'UPDATE', 'DELETE'
    old_version INTEGER,
    new_version INTEGER,
    client_info JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 错误码定义

### 认证相关错误
- `AUTH_001`: 登录令牌无效
- `AUTH_002`: 登录码无效或已过期
- `AUTH_003`: authToken 无效或已过期
- `AUTH_004`: authToken 已被撤销
- `AUTH_005`: 用户账号已被禁用

### WebSocket 连接错误
- `WS_001`: 连接认证失败
- `WS_002`: 消息格式无效
- `WS_003`: 不支持的消息类型
- `WS_004`: 连接已断开
- `WS_005`: 服务器内部错误

### 数据同步错误
- `SYNC_001`: 配置数据格式无效
- `SYNC_002`: 版本冲突，需要重新获取最新数据
- `SYNC_003`: 设备配置不存在
- `SYNC_004`: 数据库操作失败
- `SYNC_005`: 配置数据过大

## 消息流程图

### 初始连接和同步流程
```
客户端                    服务端
  |                        |
  |-- WebSocket 连接 ------>|
  |<----- 连接成功 ---------|
  |                        |
  |-- auth 消息 ----------->|
  |                        |-- 验证 authToken
  |<-- auth_success -------|
  |                        |
  |-- get_cloud_config --->|
  |                        |-- 查询用户配置
  |<-- cloud_config -------|
  |                        |
  |-- 比较本地和云端数据 ---|
  |                        |
  |-- sync_to_cloud ------>|  (如果本地更新)
  |<-- sync_success -------|
```

### 实时同步流程
```
设备A                    服务端                    设备B
  |                        |                        |
  |-- 配置更新 ----------->|                        |
  |                        |-- 保存到数据库 --------|
  |<-- sync_success -------|                        |
  |                        |-- config_updated ----->|
  |                        |                        |-- 应用新配置
```

## 性能指标建议

### 响应时间要求
- API 接口响应时间 < 200ms
- WebSocket 消息处理时间 < 50ms
- 配置数据同步延迟 < 1s

### 并发能力要求
- 支持至少 10,000 个并发 WebSocket 连接
- API 接口 QPS > 1000
- 数据库连接池大小建议 50-100

### 可用性要求
- 系统可用性 > 99.9%
- 数据一致性保证
- 支持优雅降级和故障恢复

## 安全建议

### 1. 传输安全
- 强制使用 HTTPS/WSS 协议
- 配置强加密套件
- 实现证书固定（Certificate Pinning）

### 2. 认证安全
- authToken 使用强随机数生成
- 实现 token 轮换机制
- 记录异常登录行为

### 3. 数据安全
- 敏感配置数据加密存储
- 实现数据访问审计
- 定期备份和恢复测试

## 监控指标

### 业务指标
- 活跃用户数
- 设备配置同步次数
- 平均配置数据大小

### 技术指标
- WebSocket 连接数
- 消息处理速度
- 数据库查询性能
- 内存和 CPU 使用率

### 异常指标
- 连接失败率
- 认证失败次数
- 数据同步失败率
- 系统错误日志数量
