package cn.ykload.flowmix.ui.component

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.delay
import kotlin.math.sqrt

/**
 * 整体增益调节滑动条组件
 * 特点：中间刻度最精细，往两侧刻度逐渐变大
 * 范围：-15dB 到 +15dB
 */
@Composable
fun GlobalGainSlider(
    value: Float,
    onValueChange: (Float) -> Unit,
    onReset: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true
) {
    val density = LocalDensity.current

    // 内部状态：用于跟踪拖拽时的临时值
    var internalValue by remember { mutableStateOf(value) }
    var isDragging by remember { mutableStateOf(false) }

    // 当外部value变化时，同步内部值（但不在拖拽时）
    LaunchedEffect(value) {
        if (!isDragging) {
            internalValue = value
        }
    }

    // 延迟更新逻辑：当拖拽停止0.1秒后自动应用更新
    LaunchedEffect(internalValue, isDragging) {
        if (isDragging && internalValue != value) {
            delay(100) // 等待0.1秒
            if (isDragging && internalValue != value) {
                onValueChange(internalValue)
            }
        }
    }

    // 主题色
    val primaryColor = MaterialTheme.colorScheme.primary
    val onSurfaceVariant = MaterialTheme.colorScheme.onSurfaceVariant
    val surfaceVariant = MaterialTheme.colorScheme.surfaceVariant
    val disabledColor = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)

    // 滑动条的实际颜色
    val sliderColor = if (enabled) primaryColor else disabledColor
    val trackColor = if (enabled) surfaceVariant else disabledColor.copy(alpha = 0.12f)
    val textColor = if (enabled) onSurfaceVariant else disabledColor
    
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // 标题和当前值
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = "整体增益",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium,
                    color = textColor
                )

                // 重置按钮
                IconButton(
                    onClick = {
                        internalValue = 0f
                        onReset()
                    },
                    enabled = enabled && (internalValue != 0f || value != 0f),
                    modifier = Modifier.size(24.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Refresh,
                        contentDescription = "重置增益",
                        modifier = Modifier.size(16.dp),
                        tint = if (enabled && (internalValue != 0f || value != 0f)) sliderColor else disabledColor
                    )
                }
            }

            Text(
                text = "${if (internalValue >= 0) "+" else ""}${"%.1f".format(internalValue)}dB",
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Bold,
                color = sliderColor
            )
        }
        
        // 自定义滑动条
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(60.dp)
        ) {
            var sliderWidth by remember { mutableStateOf(0f) }
            
            Canvas(
                modifier = Modifier
                    .fillMaxSize()
                    .pointerInput(enabled) {
                        if (enabled) {
                            detectDragGestures(
                                onDragStart = { offset ->
                                    isDragging = true
                                    // 立即更新内部值到当前点击位置
                                    val thumbPadding = 16.dp.toPx()
                                    val trackWidth = sliderWidth - 2 * thumbPadding
                                    val adjustedPosition = (offset.x - thumbPadding).coerceIn(0f, trackWidth)
                                    val newValue = positionToValue(adjustedPosition, trackWidth)
                                    internalValue = newValue
                                },
                                onDragEnd = {
                                    isDragging = false
                                    // 拖拽结束时立即应用更新
                                    if (internalValue != value) {
                                        onValueChange(internalValue)
                                    }
                                }
                            ) { change, _ ->
                                val thumbPadding = 16.dp.toPx()
                                val trackWidth = sliderWidth - 2 * thumbPadding
                                val adjustedPosition = (change.position.x - thumbPadding).coerceIn(0f, trackWidth)
                                val newValue = positionToValue(adjustedPosition, trackWidth)
                                internalValue = newValue
                            }
                        }
                    }
            ) {
                sliderWidth = size.width
                drawCustomSlider(
                    value = internalValue,
                    sliderColor = sliderColor,
                    trackColor = trackColor,
                    textColor = textColor
                )
            }
        }
        
        // 说明文字
        Text(
            text = "调节此滑条会自动关闭等响度补偿",
            style = MaterialTheme.typography.bodySmall,
            color = textColor.copy(alpha = 0.7f),
            textAlign = TextAlign.Center,
            modifier = Modifier.fillMaxWidth()
        )
    }
}

/**
 * 将位置转换为增益值
 */
private fun positionToValue(position: Float, width: Float): Float {
    val normalizedPosition = (position / width).coerceIn(0f, 1f)

    // 使用非线性映射，中间区域更精细
    return when {
        normalizedPosition < 0.5f -> {
            // 左半部分：-15dB 到 0dB，完全照搬右侧逻辑
            val leftNormalized = (0.5f - normalizedPosition) * 2f // 1 到 0（从左到中心）
            // 使用平方函数使中间更精细（完全照搬右侧）
            -15f * (leftNormalized * leftNormalized)
        }
        else -> {
            // 右半部分：0dB 到 +15dB
            val rightNormalized = (normalizedPosition - 0.5f) * 2f // 0 到 1
            // 使用平方函数使中间更精细
            15f * (rightNormalized * rightNormalized)
        }
    }
}

/**
 * 将增益值转换为位置
 */
private fun valueToPosition(value: Float, width: Float): Float {
    val normalizedValue = when {
        value < 0f -> {
            // 负值：-15dB 到 0dB，完全照搬右侧逻辑
            val leftValue = (-value / 15f).coerceIn(0f, 1f)
            // 平方根映射（完全照搬右侧）
            0.5f - sqrt(leftValue) * 0.5f
        }
        else -> {
            // 正值：0dB 到 +15dB
            val rightValue = (value / 15f).coerceIn(0f, 1f)
            // 平方根映射
            0.5f + sqrt(rightValue) * 0.5f
        }
    }

    return normalizedValue * width
}

/**
 * 绘制自定义滑动条
 */
private fun DrawScope.drawCustomSlider(
    value: Float,
    sliderColor: Color,
    trackColor: Color,
    textColor: Color
) {
    val trackHeight = 4.dp.toPx()
    val thumbWidth = 3.dp.toPx()
    val thumbHeight = 20.dp.toPx()
    val thumbPadding = 16.dp.toPx() // 滑块左右留出的空间
    val centerY = size.height / 2f

    // 绘制轨道
    drawLine(
        color = trackColor,
        start = Offset(thumbPadding, centerY),
        end = Offset(size.width - thumbPadding, centerY),
        strokeWidth = trackHeight
    )

    // 计算滑块位置
    val trackWidth = size.width - 2 * thumbPadding
    val thumbPosition = valueToPosition(value, trackWidth) + thumbPadding

    // 绘制活动轨道（从中心到滑块）
    val centerX = size.width / 2f
    val activeStart = if (value >= 0) centerX else thumbPosition
    val activeEnd = if (value >= 0) thumbPosition else centerX

    drawLine(
        color = sliderColor,
        start = Offset(activeStart, centerY),
        end = Offset(activeEnd, centerY),
        strokeWidth = trackHeight
    )

    // 绘制滑块（竖线样式）
    drawLine(
        color = sliderColor,
        start = Offset(thumbPosition, centerY - thumbHeight / 2),
        end = Offset(thumbPosition, centerY + thumbHeight / 2),
        strokeWidth = thumbWidth
    )

    // 绘制刻度标记
    drawScaleMarks(textColor, thumbPadding)
}

/**
 * 绘制刻度标记
 */
private fun DrawScope.drawScaleMarks(textColor: Color, thumbPadding: Float) {
    val markHeight = 8.dp.toPx()
    val centerY = size.height / 2f
    val trackWidth = size.width - 2 * thumbPadding

    // 主要刻度：-15, -10, -5, 0, +5, +10, +15
    val majorMarks = listOf(-15f, -10f, -5f, 0f, 5f, 10f, 15f)

    majorMarks.forEach { markValue ->
        val markPosition = valueToPosition(markValue, trackWidth) + thumbPadding

        drawLine(
            color = textColor,
            start = Offset(markPosition, centerY + 12.dp.toPx()),
            end = Offset(markPosition, centerY + 12.dp.toPx() + markHeight),
            strokeWidth = 1.dp.toPx()
        )
    }

    // 次要刻度：在中间区域添加更多刻度
    val minorMarks = listOf(-2f, -1f, 1f, 2f)

    minorMarks.forEach { markValue ->
        val markPosition = valueToPosition(markValue, trackWidth) + thumbPadding

        drawLine(
            color = textColor.copy(alpha = 0.5f),
            start = Offset(markPosition, centerY + 12.dp.toPx()),
            end = Offset(markPosition, centerY + 12.dp.toPx() + markHeight * 0.6f),
            strokeWidth = 0.5.dp.toPx()
        )
    }
}
