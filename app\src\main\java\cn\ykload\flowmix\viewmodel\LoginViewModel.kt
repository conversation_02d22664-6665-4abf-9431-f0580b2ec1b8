package cn.ykload.flowmix.viewmodel

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import cn.ykload.flowmix.auth.AuthManager
import cn.ykload.flowmix.auth.LoginResult
import cn.ykload.flowmix.network.NetworkManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 登录界面状态
 */
data class LoginUiState(
    val loginCode: String = "",
    val isLoading: Boolean = false,
    val isLoggedIn: Boolean = false,
    val errorMessage: String? = null
)

/**
 * 登录 ViewModel
 */
class LoginViewModel(application: Application) : AndroidViewModel(application) {
    
    companion object {
        private const val TAG = "LoginViewModel"
    }
    
    private val authManager = AuthManager.getInstance(application, NetworkManager.flowSyncApi)
    
    // UI状态
    private val _uiState = MutableStateFlow(LoginUiState())
    val uiState: StateFlow<LoginUiState> = _uiState.asStateFlow()
    
    init {
        // 监听认证状态变化
        viewModelScope.launch {
            authManager.isLoggedIn.collect { isLoggedIn ->
                _uiState.value = _uiState.value.copy(isLoggedIn = isLoggedIn)
            }
        }
        
        // 尝试自动登录
        tryAutoLogin()
    }
    
    /**
     * 更新登录码
     */
    fun updateLoginCode(loginCode: String) {
        _uiState.value = _uiState.value.copy(loginCode = loginCode)
    }
    
    /**
     * 执行登录
     */
    fun login() {
        val loginCode = _uiState.value.loginCode
        if (loginCode.length != 6) {
            _uiState.value = _uiState.value.copy(errorMessage = "请输入6位数字登录码")
            return
        }
        
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(
                    isLoading = true,
                    errorMessage = null
                )
                
                when (val result = authManager.login(loginCode)) {
                    is LoginResult.Success -> {
                        Log.d(TAG, "登录成功")
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            isLoggedIn = true
                        )
                    }
                    is LoginResult.Error -> {
                        Log.w(TAG, "登录失败: ${result.message}")
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            errorMessage = result.message
                        )
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "登录过程中发生异常", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = "登录失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 尝试自动登录
     */
    private fun tryAutoLogin() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                
                val success = authManager.autoLogin()
                if (success) {
                    Log.d(TAG, "自动登录成功")
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        isLoggedIn = true
                    )
                } else {
                    Log.d(TAG, "自动登录失败，需要手动登录")
                    _uiState.value = _uiState.value.copy(isLoading = false)
                }
            } catch (e: Exception) {
                Log.e(TAG, "自动登录过程中发生异常", e)
                _uiState.value = _uiState.value.copy(isLoading = false)
            }
        }
    }
    
    /**
     * 清除错误消息
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }
    
    /**
     * 登出
     */
    fun logout() {
        viewModelScope.launch {
            try {
                authManager.logout()
                _uiState.value = _uiState.value.copy(
                    loginCode = "",
                    isLoggedIn = false,
                    errorMessage = null
                )
                Log.d(TAG, "用户已登出")
            } catch (e: Exception) {
                Log.e(TAG, "登出过程中发生异常", e)
            }
        }
    }
}
