{"options": {"hasObfuscationDictionary": false, "hasClassObfuscationDictionary": false, "hasPackageObfuscationDictionary": false, "keepAttributes": {"isAnnotationDefaultKept": true, "isEnclosingMethodKept": true, "isExceptionsKept": false, "isInnerClassesKept": true, "isLocalVariableTableKept": false, "isLocalVariableTypeTableKept": false, "isMethodParametersKept": false, "isPermittedSubclassesKept": false, "isRuntimeInvisibleAnnotationsKept": true, "isRuntimeInvisibleParameterAnnotationsKept": true, "isRuntimeInvisibleTypeAnnotationsKept": true, "isRuntimeVisibleAnnotationsKept": true, "isRuntimeVisibleParameterAnnotationsKept": true, "isRuntimeVisibleTypeAnnotationsKept": true, "isSignatureKept": true, "isSourceDebugExtensionKept": false, "isSourceDirKept": false, "isSourceFileKept": false, "isStackMapTableKept": false}, "isAccessModificationEnabled": true, "isFlattenPackageHierarchyEnabled": false, "isObfuscationEnabled": true, "isOptimizationsEnabled": true, "isProGuardCompatibilityModeEnabled": false, "isProtoLiteOptimizationEnabled": false, "isRepackageClassesEnabled": false, "isShrinkingEnabled": true, "apiModeling": {}, "minApiLevel": "28", "isDebugModeEnabled": false}, "baselineProfileRewriting": {}, "compilation": {"buildTimeNs": 18714227600, "numberOfThreads": 20}, "dexFiles": [{"checksum": "6e487e92d51c3f2bbec37cda0231bdabd9daf3813cdd7149363402909202d3d6", "startup": false}], "stats": {"noObfuscationPercentage": 6.63, "noOptimizationPercentage": 7.48, "noShrinkingPercentage": 6.72}, "featureSplits": {"featureSplits": [{"dexFiles": []}], "isolatedSplits": false}, "resourceOptimization": {"isOptimizedShrinkingEnabled": false}, "version": "8.12.14"}