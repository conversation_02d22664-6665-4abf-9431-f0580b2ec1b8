# 图表标签功能实现说明

## 功能概述

为AutoEq和频响页面的图表添加了右上角文字提示功能，当用户切换到某个页面时，图表标签会以动画形式显示。

## 最新修复

### 修复内容
1. **文字垂直居中问题**: 使用fontMetrics计算正确的baseline，确保文字在圆角矩形中垂直居中
2. **右边缘对齐问题**: 修正最终位置计算，确保文字右边缘精确对齐到Card右边缘

## 实现的功能

### 1. 图表标签显示
- **AutoEq页面**: 显示"AutoEq曲线"标签
- **频响页面**: 显示"频响曲线"标签
- **位置**: 紧贴Card右上角（右边缘和上边缘各留16dp内边距）
- **背景**: 圆角矩形背景（8dp圆角，80%透明度）
- **对齐方式**: 动画过程中居中，最终位置右对齐

### 2. 动画效果
当用户切换到包含图表的页面时：

1. **第一阶段（初始显示）**:
   - 标签在图表中央以大字号（28sp）显示
   - 使用弹性动画效果（Spring.DampingRatioMediumBouncy）
   - 从缩放0到1的进入动画
   - 文字居中对齐，带圆角矩形背景

2. **第二阶段（停留）**:
   - 在中央停留0.8秒

3. **第三阶段（移动到最终位置）**:
   - 从中央移动到右上角
   - 字体大小从28sp缩小到14sp
   - 使用600ms的EaseInOutCubic动画
   - 透明度从1.0变为0.8（常驻状态）
   - 文字右对齐到边缘位置

## 修改的文件

### 1. EqCurveChart.kt
- 添加了`showChartLabel`、`chartLabelText`、`onPageEntered`参数
- 实现了`drawChartLabel`函数
- 添加了标签动画状态管理

### 2. FrequencyResponseChart.kt
- 添加了相同的标签参数
- 实现了`drawFrequencyResponseChartLabel`函数
- 添加了标签动画状态管理

### 3. MainNavigationScreen.kt
- 添加了页面切换时的状态管理
- 为每个页面独立管理`pageEntered`状态
- 在页面切换时重置并触发动画

### 4. MainScreen.kt
- 添加了`hasPageEntered`参数
- 修改了EqCurveChart的调用，启用标签功能

### 5. FrequencyResponseScreen.kt
- 添加了`hasPageEntered`参数
- 修改了FrequencyResponseChart的调用，启用标签功能

## 技术实现细节

### 动画状态管理
```kotlin
// 图表标签动画状态
var labelAnimationProgress by remember { mutableStateOf(0f) }
var labelPositionProgress by remember { mutableStateOf(0f) }
var labelScaleProgress by remember { mutableStateOf(0f) }
```

### 页面切换监听
```kotlin
LaunchedEffect(selectedTab) {
    when (selectedTab) {
        BottomNavItem.AUTOEQ -> {
            autoEqPageEntered = false
            delay(100) // 等待页面切换动画开始
            autoEqPageEntered = true
        }
        // ...
    }
}
```

### 标签位置计算
```kotlin
// 计算标签位置
val centerX = chartWidth / 2
val centerY = chartHeight / 2
val targetX = chartWidth - 16.dp.toPx() // 紧贴右边缘
val targetY = 16.dp.toPx() // 紧贴上边缘

// 插值计算当前位置
val currentX = centerX + (targetX - centerX) * labelPositionProgress
val currentY = centerY + (targetY - centerY) * labelPositionProgress
```

## 使用方法

功能会在页面切换时自动触发，无需手动调用。每次切换到AutoEq或频响页面时，对应的图表标签动画会自动播放。

## 注意事项

1. 标签动画只在页面切换时触发，不会在数据更新时重复播放
2. 动画完成后标签会常驻在右上角，透明度为0.8
3. 动画过程中文字保持居中对齐，最终位置右对齐确保紧贴右边缘
4. 圆角矩形背景提供更好的视觉效果和文字可读性
5. 动画时序经过优化，确保与页面切换动画协调
6. 文字尺寸测量确保背景矩形大小适配不同长度的标签文本
