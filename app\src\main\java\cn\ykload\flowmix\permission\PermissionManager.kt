package cn.ykload.flowmix.permission

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.Settings
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat

/**
 * 权限管理器
 * 处理不同Android版本的文件访问权限
 */
class PermissionManager(private val context: Context) {
    
    companion object {
        const val REQUEST_CODE_STORAGE_PERMISSION = 1001
        const val REQUEST_CODE_MANAGE_EXTERNAL_STORAGE = 1002
        const val REQUEST_CODE_AUDIO_PERMISSION = 1003
        const val REQUEST_CODE_MEDIA_PERMISSIONS = 1004
        const val REQUEST_CODE_BLUETOOTH_PERMISSIONS = 1005
    }
    
    /**
     * 检查是否有音频设置权限
     */
    fun hasAudioPermission(): Boolean {
        return ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.MODIFY_AUDIO_SETTINGS
        ) == PackageManager.PERMISSION_GRANTED
    }

    /**
     * 检查是否有蓝牙权限
     */
    fun hasBluetoothPermission(): Boolean {
        return when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
                // Android 12+ 检查新的蓝牙权限
                ContextCompat.checkSelfPermission(
                    context,
                    Manifest.permission.BLUETOOTH_CONNECT
                ) == PackageManager.PERMISSION_GRANTED
            }
            else -> {
                // Android 11及以下检查传统蓝牙权限
                ContextCompat.checkSelfPermission(
                    context,
                    Manifest.permission.BLUETOOTH
                ) == PackageManager.PERMISSION_GRANTED
            }
        }
    }
    
    /**
     * 检查是否有文件读取权限
     */
    fun hasStoragePermission(): Boolean {
        return when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU -> {
                // Android 13+ 检查媒体权限
                hasMediaPermissions()
            }
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.R -> {
                // Android 11-12 检查管理外部存储权限或传统存储权限
                Environment.isExternalStorageManager() || hasLegacyStoragePermission()
            }
            else -> {
                // Android 10及以下 检查传统存储权限
                hasLegacyStoragePermission()
            }
        }
    }

    /**
     * 检查是否有通知权限
     */
    fun hasNotificationPermission(): Boolean {
        return when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU -> {
                // Android 13+ 需要通知权限
                ContextCompat.checkSelfPermission(
                    context,
                    Manifest.permission.POST_NOTIFICATIONS
                ) == PackageManager.PERMISSION_GRANTED
            }
            else -> {
                // Android 12及以下默认有通知权限
                true
            }
        }
    }
    
    /**
     * 检查传统存储权限
     */
    private fun hasLegacyStoragePermission(): Boolean {
        return ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.READ_EXTERNAL_STORAGE
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * 检查Android 13+的媒体权限
     */
    private fun hasMediaPermissions(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.READ_MEDIA_AUDIO
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            true
        }
    }
    
    /**
     * 获取需要请求的权限列表
     */
    fun getPermissionsToRequest(): Array<String> {
        val permissions = mutableListOf<String>()

        if (!hasAudioPermission()) {
            permissions.add(Manifest.permission.MODIFY_AUDIO_SETTINGS)
        }

        if (!hasBluetoothPermission()) {
            when {
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
                    permissions.add(Manifest.permission.BLUETOOTH_CONNECT)
                    permissions.add(Manifest.permission.BLUETOOTH_SCAN)
                }
                else -> {
                    permissions.add(Manifest.permission.BLUETOOTH)
                    permissions.add(Manifest.permission.BLUETOOTH_ADMIN)
                }
            }
        }

        if (!hasStoragePermission()) {
            when {
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU -> {
                    permissions.add(Manifest.permission.READ_MEDIA_AUDIO)
                }
                else -> {
                    permissions.add(Manifest.permission.READ_EXTERNAL_STORAGE)
                }
            }
        }

        if (!hasNotificationPermission()) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                permissions.add(Manifest.permission.POST_NOTIFICATIONS)
            }
        }

        return permissions.toTypedArray()
    }
    
    /**
     * 请求存储权限
     */
    fun requestStoragePermission(activity: Activity) {
        when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU -> {
                // Android 13+ 请求媒体权限
                requestMediaPermissions(activity)
            }
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.R -> {
                // Android 11-12 优先请求管理外部存储权限
                if (!Environment.isExternalStorageManager()) {
                    requestManageExternalStoragePermission(activity)
                } else {
                    requestLegacyStoragePermission(activity)
                }
            }
            else -> {
                // Android 10及以下 请求传统存储权限
                requestLegacyStoragePermission(activity)
            }
        }
    }
    
    /**
     * 请求传统存储权限
     */
    private fun requestLegacyStoragePermission(activity: Activity) {
        ActivityCompat.requestPermissions(
            activity,
            arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE),
            REQUEST_CODE_STORAGE_PERMISSION
        )
    }
    
    /**
     * 请求Android 13+媒体权限
     */
    private fun requestMediaPermissions(activity: Activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ActivityCompat.requestPermissions(
                activity,
                arrayOf(
                    Manifest.permission.READ_MEDIA_AUDIO,
                    Manifest.permission.READ_MEDIA_IMAGES,
                    Manifest.permission.READ_MEDIA_VIDEO
                ),
                REQUEST_CODE_MEDIA_PERMISSIONS
            )
        }
    }
    
    /**
     * 请求管理外部存储权限 (Android 11+)
     */
    private fun requestManageExternalStoragePermission(activity: Activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            try {
                val intent = Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION)
                intent.data = Uri.parse("package:${context.packageName}")
                activity.startActivityForResult(intent, REQUEST_CODE_MANAGE_EXTERNAL_STORAGE)
            } catch (e: Exception) {
                // 如果无法打开设置页面，回退到传统权限请求
                requestLegacyStoragePermission(activity)
            }
        }
    }
    
    /**
     * 检查是否应该显示权限说明
     */
    fun shouldShowStoragePermissionRationale(activity: Activity): Boolean {
        return when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU -> {
                ActivityCompat.shouldShowRequestPermissionRationale(
                    activity,
                    Manifest.permission.READ_MEDIA_AUDIO
                )
            }
            else -> {
                ActivityCompat.shouldShowRequestPermissionRationale(
                    activity,
                    Manifest.permission.READ_EXTERNAL_STORAGE
                )
            }
        }
    }
    
    /**
     * 检查是否应该显示音频权限说明
     */
    fun shouldShowAudioPermissionRationale(activity: Activity): Boolean {
        return ActivityCompat.shouldShowRequestPermissionRationale(
            activity,
            Manifest.permission.MODIFY_AUDIO_SETTINGS
        )
    }

    /**
     * 检查是否应该显示蓝牙权限说明
     */
    fun shouldShowBluetoothPermissionRationale(activity: Activity): Boolean {
        return when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
                ActivityCompat.shouldShowRequestPermissionRationale(
                    activity,
                    Manifest.permission.BLUETOOTH_CONNECT
                )
            }
            else -> {
                ActivityCompat.shouldShowRequestPermissionRationale(
                    activity,
                    Manifest.permission.BLUETOOTH
                )
            }
        }
    }
    
    /**
     * 打开应用设置页面
     */
    fun openAppSettings(activity: Activity) {
        try {
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
            intent.data = Uri.parse("package:${context.packageName}")
            activity.startActivity(intent)
        } catch (e: Exception) {
            // 如果无法打开应用设置，打开通用设置
            val intent = Intent(Settings.ACTION_SETTINGS)
            activity.startActivity(intent)
        }
    }
    
    /**
     * 获取权限状态描述
     */
    fun getPermissionStatusDescription(): String {
        val audioStatus = if (hasAudioPermission()) "已授权" else "未授权"
        val storageStatus = if (hasStoragePermission()) "已授权" else "未授权"
        
        return "音频权限: $audioStatus, 存储权限: $storageStatus"
    }
}
