package cn.ykload.flowmix.service

import android.content.Context
import android.content.SharedPreferences
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.whenever
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * ServiceManager 单元测试
 */
@ExperimentalCoroutinesApi
@RunWith(AndroidJUnit4::class)
class ServiceManagerTest {

    @Mock
    private lateinit var mockContext: Context
    
    @Mock
    private lateinit var mockSharedPreferences: SharedPreferences
    
    @Mock
    private lateinit var mockEditor: SharedPreferences.Editor

    private lateinit var serviceManager: ServiceManager

    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        
        // 设置 SharedPreferences mock
        whenever(mockContext.getSharedPreferences("flowmix_service_prefs", Context.MODE_PRIVATE))
            .thenReturn(mockSharedPreferences)
        whenever(mockSharedPreferences.edit()).thenReturn(mockEditor)
        whenever(mockEditor.putBoolean(any(), any())).thenReturn(mockEditor)
        whenever(mockEditor.apply()).then { }
        
        // 默认设置值
        whenever(mockSharedPreferences.getBoolean("keep_alive_enabled", true)).thenReturn(true)
        whenever(mockSharedPreferences.getBoolean("auto_start_enabled", true)).thenReturn(true)
    }

    @Test
    fun testServiceManagerInitialization() = runTest {
        serviceManager = ServiceManager.getInstance(mockContext)
        
        // 验证初始状态
        assertTrue(serviceManager.isKeepAliveEnabled.value)
        assertTrue(serviceManager.isAutoStartEnabled.value)
        assertFalse(serviceManager.isKeepAliveServiceRunning.value)
    }

    @Test
    fun testSetKeepAliveEnabled() = runTest {
        serviceManager = ServiceManager.getInstance(mockContext)
        
        // 测试禁用保活服务
        serviceManager.setKeepAliveEnabled(false)
        
        assertFalse(serviceManager.isKeepAliveEnabled.value)
    }

    @Test
    fun testSetAutoStartEnabled() = runTest {
        serviceManager = ServiceManager.getInstance(mockContext)
        
        // 测试禁用自动启动
        serviceManager.setAutoStartEnabled(false)
        
        assertFalse(serviceManager.isAutoStartEnabled.value)
    }

    @Test
    fun testUpdateFlowmixStatus() = runTest {
        serviceManager = ServiceManager.getInstance(mockContext)
        
        // 测试更新Flowmix状态
        serviceManager.updateFlowmixStatus(true)
        
        assertTrue(serviceManager.isFlowmixEnabled.value)
        
        serviceManager.updateFlowmixStatus(false)
        
        assertFalse(serviceManager.isFlowmixEnabled.value)
    }

    @Test
    fun testGetServiceStats() = runTest {
        serviceManager = ServiceManager.getInstance(mockContext)
        
        val stats = serviceManager.getServiceStats()
        
        assertEquals(false, stats.isKeepAliveServiceRunning)
        assertEquals(false, stats.isFlowmixEnabled)
        assertEquals(true, stats.isKeepAliveEnabled)
        assertEquals(true, stats.isAutoStartEnabled)
    }

    private fun <T> any(): T {
        return null as T
    }
}
