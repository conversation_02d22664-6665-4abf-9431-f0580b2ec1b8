package cn.ykload.flowmix.audio

import android.content.Context
import android.media.MediaPlayer
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 音效管理器
 * 负责播放Flowmix状态切换时的提示音
 */
class SoundEffectManager(private val context: Context) {
    
    companion object {
        private const val TAG = "SoundEffectManager"
    }
    
    private var mediaPlayer: MediaPlayer? = null
    
    /**
     * 播放Flowmix启用音效
     */
    suspend fun playFlowmixOnSound() {
        withContext(Dispatchers.IO) {
            try {
                playSound("flowmix_on.mp3")
                Log.d(TAG, "播放Flowmix启用音效")
            } catch (e: Exception) {
                Log.e(TAG, "播放Flowmix启用音效失败", e)
            }
        }
    }
    
    /**
     * 播放Flowmix禁用音效
     */
    suspend fun playFlowmixOffSound() {
        withContext(Dispatchers.IO) {
            try {
                playSound("flowmix_off.mp3")
                Log.d(TAG, "播放Flowmix禁用音效")
            } catch (e: Exception) {
                Log.e(TAG, "播放Flowmix禁用音效失败", e)
            }
        }
    }
    
    /**
     * 播放指定的音效文件
     */
    private fun playSound(fileName: String) {
        try {
            // 释放之前的MediaPlayer
            releaseMediaPlayer()
            
            // 从assets目录加载音效文件
            val assetFileDescriptor = context.assets.openFd("sounds/$fileName")
            
            mediaPlayer = MediaPlayer().apply {
                setDataSource(
                    assetFileDescriptor.fileDescriptor,
                    assetFileDescriptor.startOffset,
                    assetFileDescriptor.length
                )
                prepareAsync()
                setOnPreparedListener { player ->
                    player.start()
                }
                setOnCompletionListener {
                    releaseMediaPlayer()
                }
                setOnErrorListener { _, what, extra ->
                    Log.e(TAG, "MediaPlayer错误: what=$what, extra=$extra")
                    releaseMediaPlayer()
                    true
                }
            }
            
            assetFileDescriptor.close()
            
        } catch (e: Exception) {
            Log.e(TAG, "播放音效失败: $fileName", e)
            releaseMediaPlayer()
        }
    }
    
    /**
     * 释放MediaPlayer资源
     */
    private fun releaseMediaPlayer() {
        try {
            mediaPlayer?.apply {
                if (isPlaying) {
                    stop()
                }
                release()
            }
        } catch (e: Exception) {
            Log.e(TAG, "释放MediaPlayer失败", e)
        } finally {
            mediaPlayer = null
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        releaseMediaPlayer()
    }
}
