package cn.ykload.flowmix

import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.animation.*
import androidx.compose.animation.core.tween
import androidx.compose.runtime.*
import androidx.lifecycle.viewmodel.compose.viewModel
import cn.ykload.flowmix.notification.NotificationHelper
import cn.ykload.flowmix.permission.PermissionManager
import cn.ykload.flowmix.service.ServiceManager
import cn.ykload.flowmix.ui.screen.MainNavigationScreen
import cn.ykload.flowmix.ui.screen.OnboardingScreen
import cn.ykload.flowmix.ui.theme.FlowmixTheme
import cn.ykload.flowmix.utils.OnboardingManager
import cn.ykload.flowmix.viewmodel.MainViewModel
import cn.ykload.flowmix.viewmodel.OnboardingViewModel

class MainActivity : ComponentActivity() {

    companion object {
        private const val TAG = "MainActivity"
    }

    private lateinit var permissionManager: PermissionManager
    private lateinit var serviceManager: ServiceManager
    private lateinit var onboardingManager: OnboardingManager
    private var currentOnboardingViewModel: OnboardingViewModel? = null
    private var currentMainViewModel: MainViewModel? = null

    // 权限请求结果处理
    private val permissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        // 根据当前状态调用相应的ViewModel处理权限结果
        currentOnboardingViewModel?.onPermissionResult(permissions)
        currentMainViewModel?.onPermissionResult(permissions)
    }

    @OptIn(ExperimentalAnimationApi::class)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        permissionManager = PermissionManager(this)
        serviceManager = ServiceManager.getInstance(this)
        onboardingManager = OnboardingManager(this)

        // 处理通知点击事件
        handleNotificationIntent(intent)

        enableEdgeToEdge()
        setContent {
            FlowmixTheme {
                var showOnboarding by remember { mutableStateOf(onboardingManager.shouldShowOnboarding(permissionManager)) }

                // 使用AnimatedContent实现过渡动画
                AnimatedContent(
                    targetState = showOnboarding,
                    transitionSpec = {
                        if (targetState) {
                            // 进入欢迎页：无动画（初始状态）
                            EnterTransition.None with ExitTransition.None
                        } else {
                            // 从欢迎页到主页面：欢迎页淡出，主页面缩放淡入
                            (scaleIn(
                                initialScale = 0.8f,
                                animationSpec = tween(600)
                            ) + fadeIn(
                                animationSpec = tween(600)
                            )) with fadeOut(
                                animationSpec = tween(300)
                            )
                        }
                    },
                    label = "onboarding_to_main_transition"
                ) { isOnboarding ->
                    if (isOnboarding) {
                        val onboardingViewModel: OnboardingViewModel = viewModel()
                        currentOnboardingViewModel = onboardingViewModel
                        currentMainViewModel = null

                        onboardingViewModel.setPermissionManager(permissionManager)
                        onboardingViewModel.setPermissionLauncher(permissionLauncher)

                        OnboardingScreen(
                            viewModel = onboardingViewModel,
                            permissionManager = permissionManager,
                            onComplete = {
                                onboardingManager.markOnboardingCompleted()
                                showOnboarding = false
                            }
                        )
                    } else {
                        val mainViewModel: MainViewModel = viewModel()
                        currentMainViewModel = mainViewModel
                        currentOnboardingViewModel = null

                        mainViewModel.setPermissionManager(permissionManager)
                        mainViewModel.setPermissionLauncher(permissionLauncher)
                        mainViewModel.setServiceManager(serviceManager)

                        MainNavigationScreen(
                            viewModel = mainViewModel,
                            permissionManager = permissionManager
                        )
                    }
                }
            }
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        handleNotificationIntent(intent)
    }

    override fun onDestroy() {
        super.onDestroy()
        // 注意：不要在这里停止服务，因为我们希望服务在后台继续运行
        Log.d(TAG, "MainActivity 销毁")
    }

    /**
     * 处理通知点击事件
     */
    private fun handleNotificationIntent(intent: Intent?) {
        intent?.let {
            when (it.action) {
                NotificationHelper.ACTION_OPEN_APP -> {
                    Log.d(TAG, "收到打开应用的通知点击")
                    // 只是打开应用，无需额外操作
                }
            }
        }
    }

    /**
     * 重置欢迎页状态（用于设置页面）
     */
    fun resetOnboardingStatus() {
        onboardingManager.resetOnboardingStatus()
    }

    // 移除已废弃的onRequestPermissionsResult方法
    // 现在使用ActivityResultLauncher处理权限请求

    // 移除已废弃的onActivityResult方法
    // 现在使用ActivityResultLauncher处理Activity结果
}