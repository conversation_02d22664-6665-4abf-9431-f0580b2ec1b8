package cn.ykload.flowmix.network

import cn.ykload.flowmix.data.LoginRequest
import cn.ykload.flowmix.data.LoginResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * FlowSync 云端同步 API 接口
 */
interface FlowSyncApi {
    
    /**
     * 用户登录
     * 使用6位数字登录码获取authToken
     */
    @POST("api/login")
    suspend fun login(@Body request: LoginRequest): Response<LoginResponse>
}
