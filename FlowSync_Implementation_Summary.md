# FlowSync 功能实现总结

## 概述

FlowSync 是 Flowmix 应用的新功能，实现了"同步播放设备"的核心需求。该功能能够自动检测当前音频输出设备，并为每个设备保存独立的 AutoEq 和频响配置。当用户切换音频设备时，应用会自动加载对应的配置。

## 核心功能

### 1. 自动音频设备检测
- **实时检测**：应用启动时自动开始检测当前音频输出设备
- **设备类型支持**：
  - 有线耳机 (3.5mm)
  - USB 音频设备
  - 蓝牙耳机 (A2DP/SCO)
  - 内置扬声器
  - HDMI 音频输出
- **设备变更监听**：当音频设备切换时自动触发检测和配置应用

### 2. 自动配置管理
- **独立配置**：每个音频设备自动保存独立的配置
- **配置内容**：
  - AutoEq 配置（名称、频段数值、等响度开关、整体增益）
  - 频响配置（数据源、品牌、耳机型号、测量条件、目标曲线）
- **自动保存**：当用户应用 AutoEq 效果时，自动保存到当前设备
- **持久化存储**：使用 SharedPreferences 保存配置数据

### 3. 全自动同步应用
- **智能切换**：设备切换时自动应用对应配置
- **无缝体验**：完全自动化，用户无需任何手动操作
- **状态反馈**：实时显示同步状态和设备信息
- **零干扰**：后台运行，不影响用户正常使用

## 技术架构

### 核心组件

#### 1. AudioDeviceManager
- **职责**：音频设备检测和监听
- **关键特性**：
  - 支持 Android API 23+ 的 AudioDeviceCallback
  - 兼容旧版本 API 的广播接收器
  - 设备优先级排序（有线 > USB > 蓝牙 > 扬声器）

#### 2. DeviceConfigManager
- **职责**：设备配置的存储和管理
- **关键特性**：
  - JSON 序列化存储
  - 设备唯一标识符生成
  - 配置的增删改查操作

#### 3. FlowSyncViewModel
- **职责**：UI 状态管理和业务逻辑协调
- **关键特性**：
  - 设备状态监听
  - 配置自动应用
  - 与 MainViewModel 的集成

#### 4. FlowSyncScreen
- **职责**：用户界面展示
- **关键特性**：
  - 当前设备状态显示
  - 设备配置管理界面
  - 保存当前配置功能

### 数据模型

#### DeviceConfig
```kotlin
data class DeviceConfig(
    val deviceId: String,           // 设备唯一标识
    val deviceName: String,         // 设备名称
    val deviceType: String,         // 设备类型
    val autoEqConfig: AutoEqConfig?, // AutoEq配置
    val frequencyResponseConfig: FrequencyResponseConfig?, // 频响配置
    val lastUpdated: Long          // 最后更新时间
)
```

#### AutoEqConfig
```kotlin
data class AutoEqConfig(
    val name: String,                           // 配置名称
    val bands: List<EqBandConfig>,             // 频段配置
    val isLoudnessCompensationEnabled: Boolean, // 等响度补偿
    val globalGain: Float                      // 整体增益
)
```

#### FrequencyResponseConfig
```kotlin
data class FrequencyResponseConfig(
    val dataSource: String?,        // 数据源
    val brand: String?,            // 品牌
    val headphone: String?,        // 耳机型号
    val measurementCondition: String?, // 测量条件
    val targetCurve: String?       // 目标曲线
)
```

## 用户界面

### FlowSync 页面布局

1. **功能介绍卡片**
   - FlowSync 功能说明
   - 支持的设备类型列表

2. **当前设备状态卡片**
   - 设备信息显示（名称、类型、图标）
   - 同步状态指示器
   - 配置状态（AutoEq、频响）
   - 监听控制按钮
   - 保存当前配置按钮

3. **设备配置管理卡片**
   - 已配置设备列表
   - 配置详情显示
   - 删除配置功能

### 交互流程

1. **自动启动**：应用启动时自动开始设备监听
2. **设备检测**：自动检测当前音频输出设备
3. **配置应用**：如果设备有保存的配置，自动应用
4. **自动保存**：当用户在 AutoEq 页面应用效果时，自动保存配置到当前设备
5. **设备切换**：当用户切换音频设备时，自动检测并应用对应配置
6. **无缝体验**：整个过程完全自动化，用户无需任何手动操作

## 集成方式

### 与 MainViewModel 集成
- MainViewModel 实现 `DeviceConfigApplyCallback` 接口
- FlowSyncViewModel 通过回调接口通知 MainViewModel 应用配置
- 实现了 AutoEq 配置的自动应用
- 频响配置的应用预留了接口（需要进一步架构调整）

### 导航集成
- 在 MainNavigationScreen 中集成 FlowSyncScreen
- 替换了原有的占位符 SyncScreen
- 保持了与其他页面一致的导航体验

## 权限要求

FlowSync 功能使用了以下权限：
- `MODIFY_AUDIO_SETTINGS`：用于音频设置修改（已有）
- 无需额外权限，复用现有权限体系

## 兼容性

- **最低 API 级别**：Android 9.0 (API 28)
- **推荐 API 级别**：Android 6.0+ (API 23+) 以获得完整的设备检测功能
- **向后兼容**：在旧版本 Android 上提供基础的设备检测功能

## 未来扩展

1. **跨设备同步**：第二个模块的实现
2. **云端配置同步**：配置数据的云端备份和同步
3. **更多设备类型**：支持更多音频设备类型
4. **配置导入导出**：配置文件的导入导出功能
5. **智能推荐**：基于设备类型的配置推荐

## 文件结构

```
app/src/main/java/cn/ykload/flowmix/
├── audio/
│   └── AudioDeviceManager.kt          # 音频设备检测管理
├── data/
│   └── DeviceConfig.kt                # 设备配置数据模型
├── storage/
│   └── DeviceConfigManager.kt         # 设备配置存储管理
├── viewmodel/
│   ├── FlowSyncViewModel.kt           # FlowSync ViewModel
│   └── MainViewModel.kt               # 主 ViewModel（已修改）
└── ui/screen/
    ├── FlowSyncScreen.kt              # FlowSync 页面
    └── MainNavigationScreen.kt        # 主导航页面（已修改）
```

## 总结

FlowSync 功能成功实现了音频设备的自动检测和配置管理，为用户提供了无缝的多设备音频体验。该功能具有良好的架构设计、完整的错误处理和用户友好的界面，为后续的功能扩展奠定了坚实的基础。
