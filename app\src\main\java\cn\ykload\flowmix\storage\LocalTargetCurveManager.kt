package cn.ykload.flowmix.storage

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import cn.ykload.flowmix.data.MeasurementCondition
import cn.ykload.flowmix.data.TargetCurve
import cn.ykload.flowmix.data.TargetCurveData
import cn.ykload.flowmix.data.TargetCurveSource
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

/**
 * 本地目标曲线管理器
 * 负责本地目标曲线的增删改查操作
 */
class LocalTargetCurveManager(private val context: Context) {
    
    companion object {
        private const val TAG = "LocalTargetCurveManager"
        private const val PREFS_NAME = "local_target_curves"
        private const val KEY_TARGET_CURVES = "target_curves"
        private const val TARGET_CURVES_DIR = "target_curves"
    }
    
    private val sharedPreferences: SharedPreferences = 
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    private val gson = Gson()
    
    /**
     * 获取本地目标曲线目录
     */
    private fun getTargetCurvesDir(): File {
        val dir = File(context.filesDir, TARGET_CURVES_DIR)
        if (!dir.exists()) {
            dir.mkdirs()
        }
        return dir
    }
    
    /**
     * 获取所有本地目标曲线列表
     */
    suspend fun getLocalTargetCurves(): List<TargetCurve> = withContext(Dispatchers.IO) {
        try {
            val json = sharedPreferences.getString(KEY_TARGET_CURVES, null)
            if (json != null) {
                val type = object : TypeToken<List<TargetCurve>>() {}.type
                gson.fromJson<List<TargetCurve>>(json, type) ?: emptyList()
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取本地目标曲线列表失败", e)
            emptyList()
        }
    }
    
    /**
     * 保存本地目标曲线列表
     */
    private suspend fun saveLocalTargetCurves(curves: List<TargetCurve>) = withContext(Dispatchers.IO) {
        try {
            val json = gson.toJson(curves)
            sharedPreferences.edit()
                .putString(KEY_TARGET_CURVES, json)
                .apply()
        } catch (e: Exception) {
            Log.e(TAG, "保存本地目标曲线列表失败", e)
        }
    }
    
    /**
     * 添加新的本地目标曲线
     */
    suspend fun addLocalTargetCurve(
        name: String,
        measurementData: MeasurementCondition,
        measurementCondition: String = "Default"
    ): Boolean = withContext(Dispatchers.IO) {
        try {
            // 生成唯一的文件名
            val fileName = generateUniqueFileName(name)
            val currentTime = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())
            
            // 创建目标曲线信息
            val targetCurve = TargetCurve(
                fileName = fileName,
                name = name,
                lastUpdated = currentTime,
                measurementCount = 1,
                source = TargetCurveSource.LOCAL
            )
            
            // 创建目标曲线数据
            val targetCurveData = TargetCurveData(
                name = name,
                lastUpdated = currentTime,
                frequencyData = mapOf(measurementCondition to measurementData)
            )
            
            // 保存数据到文件
            val dataFile = File(getTargetCurvesDir(), "$fileName.json")
            val dataJson = gson.toJson(targetCurveData)
            dataFile.writeText(dataJson)
            
            // 更新目标曲线列表
            val currentCurves = getLocalTargetCurves().toMutableList()
            currentCurves.add(targetCurve)
            saveLocalTargetCurves(currentCurves)
            
            Log.d(TAG, "成功添加本地目标曲线: $name")
            true
        } catch (e: Exception) {
            Log.e(TAG, "添加本地目标曲线失败", e)
            false
        }
    }
    
    /**
     * 删除本地目标曲线
     */
    suspend fun deleteLocalTargetCurve(fileName: String): Boolean = withContext(Dispatchers.IO) {
        try {
            // 删除数据文件
            val dataFile = File(getTargetCurvesDir(), "$fileName.json")
            if (dataFile.exists()) {
                dataFile.delete()
            }
            
            // 从列表中移除
            val currentCurves = getLocalTargetCurves().toMutableList()
            currentCurves.removeAll { it.fileName == fileName }
            saveLocalTargetCurves(currentCurves)
            
            Log.d(TAG, "成功删除本地目标曲线: $fileName")
            true
        } catch (e: Exception) {
            Log.e(TAG, "删除本地目标曲线失败", e)
            false
        }
    }
    
    /**
     * 获取本地目标曲线数据
     */
    suspend fun getLocalTargetCurveData(fileName: String): TargetCurveData? = withContext(Dispatchers.IO) {
        try {
            val dataFile = File(getTargetCurvesDir(), "$fileName.json")
            if (dataFile.exists()) {
                val json = dataFile.readText()
                gson.fromJson(json, TargetCurveData::class.java)
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取本地目标曲线数据失败", e)
            null
        }
    }
    
    /**
     * 生成唯一的文件名
     */
    private fun generateUniqueFileName(baseName: String): String {
        val sanitizedName = baseName.replace(Regex("[^a-zA-Z0-9\\u4e00-\\u9fa5_-]"), "_")
        val timestamp = System.currentTimeMillis()
        return "local_${sanitizedName}_$timestamp"
    }
    
    /**
     * 检查目标曲线是否为本地曲线
     */
    fun isLocalTargetCurve(fileName: String): Boolean {
        return fileName.startsWith("local_")
    }
    
    /**
     * 清理所有本地目标曲线
     */
    suspend fun clearAllLocalTargetCurves(): Boolean = withContext(Dispatchers.IO) {
        try {
            // 删除所有数据文件
            val dir = getTargetCurvesDir()
            dir.listFiles()?.forEach { file ->
                if (file.isFile && file.name.endsWith(".json")) {
                    file.delete()
                }
            }
            
            // 清空列表
            sharedPreferences.edit()
                .remove(KEY_TARGET_CURVES)
                .apply()
            
            Log.d(TAG, "成功清理所有本地目标曲线")
            true
        } catch (e: Exception) {
            Log.e(TAG, "清理本地目标曲线失败", e)
            false
        }
    }
}
