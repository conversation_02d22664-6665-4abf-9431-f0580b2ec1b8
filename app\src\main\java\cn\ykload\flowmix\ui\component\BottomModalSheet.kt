package cn.ykload.flowmix.ui.component

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.EaseInCubic
import androidx.compose.animation.core.EaseOutCubic
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Error
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties

/**
 * 底部模态框类型
 */
enum class BottomModalType {
    SUCCESS,
    ERROR,
    WARNING,
    INFO
}

/**
 * 底部模态框数据
 */
data class BottomModalData(
    val type: BottomModalType,
    val title: String,
    val message: String,
    val primaryButtonText: String? = null,
    val secondaryButtonText: String? = null,
    val onPrimaryClick: (() -> Unit)? = null,
    val onSecondaryClick: (() -> Unit)? = null,
    val onDismiss: (() -> Unit)? = null
)

/**
 * 美观的底部模态框组件
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BottomModalSheet(
    isVisible: Boolean,
    data: BottomModalData,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    // 使用内部状态来控制动画，确保退出动画能够完整播放
    var showDialog by remember { mutableStateOf(false) }
    var animateContent by remember { mutableStateOf(false) }

    // 当 isVisible 变化时，控制显示和隐藏的时序
    LaunchedEffect(isVisible) {
        if (isVisible) {
            showDialog = true
            // 短暂延迟后开始内容动画，确保 Dialog 已经显示
            kotlinx.coroutines.delay(50)
            animateContent = true
        } else {
            // 先开始退出动画
            animateContent = false
            // 等待退出动画完成后再隐藏 Dialog
            kotlinx.coroutines.delay(250) // 稍微长于退出动画时间
            showDialog = false
        }
    }

    if (showDialog) {
        var offsetY by remember { mutableStateOf(0f) }
        val density = LocalDensity.current
        val animatedOffsetY by animateFloatAsState(
            targetValue = offsetY,
            animationSpec = spring(
                dampingRatio = Spring.DampingRatioMediumBouncy,
                stiffness = Spring.StiffnessLow
            ),
            label = "offsetY"
        )

        Dialog(
            onDismissRequest = onDismiss,
            properties = DialogProperties(
                usePlatformDefaultWidth = false,
                decorFitsSystemWindows = true
            )
        ) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.5f))
                    .clickable(
                        interactionSource = remember { MutableInteractionSource() },
                        indication = null
                    ) { onDismiss() }
                    .imePadding(), // 添加输入法填充
                contentAlignment = Alignment.BottomCenter
            ) {
                AnimatedVisibility(
                    visible = animateContent,
                    enter = slideInVertically(
                        initialOffsetY = { fullHeight -> fullHeight },
                        animationSpec = tween(300, easing = EaseOutCubic)
                    ) + fadeIn(animationSpec = tween(300)),
                    exit = slideOutVertically(
                        targetOffsetY = { fullHeight -> fullHeight },
                        animationSpec = tween(200, easing = EaseInCubic)
                    ) + fadeOut(animationSpec = tween(200))
                ) {
                    Surface(
                        modifier = Modifier
                            .fillMaxWidth()
                            .wrapContentHeight()
                            .offset(y = with(density) { animatedOffsetY.toDp() })
                            .pointerInput(Unit) {
                                detectDragGestures(
                                    onDragEnd = {
                                        if (offsetY > 150) {
                                            onDismiss()
                                        } else {
                                            offsetY = 0f
                                        }
                                    }
                                ) { _, dragAmount ->
                                    val newOffset = offsetY + dragAmount.y
                                    // 只允许向下拖拽
                                    offsetY = if (newOffset > 0) newOffset else 0f
                                }
                            }
                            .clickable(
                                interactionSource = remember { MutableInteractionSource() },
                                indication = null
                            ) { /* 阻止点击事件传播 */ },
                        shape = RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp),
                        shadowElevation = 8.dp,
                        color = MaterialTheme.colorScheme.surface
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .wrapContentHeight()
                        ) {
                            // 手势线
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 12.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                Box(
                                    modifier = Modifier
                                        .width(40.dp)
                                        .height(4.dp)
                                        .background(
                                            color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.4f),
                                            shape = RoundedCornerShape(25.dp)
                                        )
                                )
                            }

                            // 内容区域
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .wrapContentHeight()
                                    .padding(horizontal = 24.dp, vertical = 8.dp)
                                    .defaultMinSize(minHeight = 120.dp) // 设置最小高度确保可见
                            ) {
                                // 图标和标题行
                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    modifier = Modifier.fillMaxWidth()
                                ) {
                                    Icon(
                                        imageVector = getIconForType(data.type),
                                        contentDescription = null,
                                        tint = getColorForType(data.type),
                                        modifier = Modifier.size(28.dp)
                                    )

                                    Spacer(modifier = Modifier.width(12.dp))

                                    Text(
                                        text = data.title,
                                        style = MaterialTheme.typography.titleLarge,
                                        fontWeight = FontWeight.SemiBold,
                                        color = getColorForType(data.type)
                                    )
                                }

                                Spacer(modifier = Modifier.height(12.dp))

                                // 消息内容
                                Text(
                                    text = data.message,
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )

                                // 按钮区域
                                if (data.primaryButtonText != null || data.secondaryButtonText != null) {
                                    Spacer(modifier = Modifier.height(20.dp))

                                    Row(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.End
                                    ) {
                                    // 次要按钮
                                    data.secondaryButtonText?.let { text ->
                                        TextButton(
                                            onClick = {
                                                data.onSecondaryClick?.invoke()
                                                onDismiss()
                                            }
                                        ) {
                                            Text(text)
                                        }
                                    }
                                    
                                    if (data.secondaryButtonText != null && data.primaryButtonText != null) {
                                        Spacer(modifier = Modifier.width(8.dp))
                                    }
                                    
                                    // 主要按钮
                                    data.primaryButtonText?.let { text ->
                                        Button(
                                            onClick = {
                                                data.onPrimaryClick?.invoke()
                                                onDismiss()
                                            },
                                            colors = ButtonDefaults.buttonColors(
                                                containerColor = getColorForType(data.type)
                                            )
                                        ) {
                                            Text(text)
                                        }
                                    }
                                    }
                                }

                                // 底部安全区域 - 增加高度确保完整显示，避免被导航栏遮挡
                                Spacer(modifier = Modifier.height(48.dp))
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun getIconForType(type: BottomModalType): ImageVector {
    return when (type) {
        BottomModalType.SUCCESS -> Icons.Default.CheckCircle
        BottomModalType.ERROR -> Icons.Default.Error
        BottomModalType.WARNING -> Icons.Default.Warning
        BottomModalType.INFO -> Icons.Default.Info
    }
}

@Composable
private fun getColorForType(type: BottomModalType): Color {
    return when (type) {
        BottomModalType.SUCCESS -> MaterialTheme.colorScheme.primary
        BottomModalType.ERROR -> MaterialTheme.colorScheme.error
        BottomModalType.WARNING -> Color(0xFFFF9800) // Orange
        BottomModalType.INFO -> MaterialTheme.colorScheme.primary
    }
}

/**
 * 简化的底部提示框
 */
@Composable
fun BottomToast(
    isVisible: Boolean,
    message: String,
    type: BottomModalType = BottomModalType.INFO,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    BottomModalSheet(
        isVisible = isVisible,
        data = BottomModalData(
            type = type,
            title = when (type) {
                BottomModalType.SUCCESS -> "成功"
                BottomModalType.ERROR -> "错误"
                BottomModalType.WARNING -> "警告"
                BottomModalType.INFO -> "提示"
            },
            message = message,
            onDismiss = onDismiss
        ),
        onDismiss = onDismiss,
        modifier = modifier
    )
    
    // 自动消失
    LaunchedEffect(isVisible) {
        if (isVisible) {
            kotlinx.coroutines.delay(3000)
            onDismiss()
        }
    }
}
