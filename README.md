# Flowmix - 全局AutoEq音频效果应用

Flowmix是一个Android应用，可以将AutoEq配置文件应用到系统全局音频输出，为所有应用提供统一的音频均衡效果。

## 功能特性

- 🎵 **全局音频效果**: 使用Android DynamicsProcessing API将EQ效果应用到系统全局音频输出
- 📁 **AutoEq文件支持**: 支持导入标准AutoEq格式的txt文件
- 🎛️ **精确控制**: 支持多达1000个频段的精确均衡调节
- 🎨 **Material Design**: 现代化的Material Design 3界面
- 🔒 **权限管理**: 智能的权限请求和管理
- ⚡ **实时控制**: 可以实时启用/禁用音频效果

## 系统要求

- Android 9.0 (API 28) 或更高版本
- 支持DynamicsProcessing API的设备

## 权限说明

应用需要以下权限：

- `MODIFY_AUDIO_SETTINGS`: 用于修改系统音频设置和应用全局音频效果
- `READ_EXTERNAL_STORAGE`: 用于读取AutoEq文件
- `READ_MEDIA_AUDIO`: Android 13+设备的媒体文件访问权限

## 使用方法

1. **启动应用**: 打开Flowmix应用
2. **检查兼容性**: 应用会自动检查设备是否支持DynamicsProcessing API
3. **授权权限**: 根据提示授予必要的权限
4. **选择文件**: 点击"选择AutoEq文件"按钮，选择你的AutoEq txt文件
5. **应用效果**: 点击"应用AutoEq"按钮将效果应用到系统全局
6. **控制效果**: 使用"启动/停止"按钮来控制效果的开关

## AutoEq文件格式

支持标准的AutoEq GraphicEQ格式：

```
GraphicEQ: 20 0; 21 0; 22 0; 23 0; 24 0; 26 0; 27 0; 29 0; 30 0; 32 0; 34 0; 36 0; 38 0; 40 0; 43 0; 45 0; 48 0; 50 0; 53 0; 56 0; 59 0; 63 0; 66 0; 70 0; 74 0; 78 0; 83 0; 87 0; 92 0; 97 0; 103 0; 109 0; 115 0; 121 0; 128 0; 136 0; 143 0; 151 0; 160 0; 169 0; 178 0; 188 0; 199 0; 210 0; 222 0; 235 0; 248 0; 262 0; 277 0; 292 0; 309 0; 326 0; 345 0; 364 0; 385 0; 406 0; 429 0; 453 0; 479 0; 506 0; 534 0; 565 0; 596 0; 630 0; 665 0; 703 0; 743 0; 784 0; 829 0; 875 0; 924 0; 977 0; 1032 0; 1090 0; 1151 0; 1216 0; 1284 0; 1357 0; 1433 0; 1514 0; 1599 0; 1689 0; 1784 0; 1885 0; 1991 0; 2103 0; 2221 0; 2347 0; 2479 0; 2618 0; 2766 0; 2921 0; 3086 0; 3260 0; 3443 0; 3637 0; 3842 0; 4058 0; 4287 0; 4528 0; 4783 0; 5052 0; 5337 0; 5637 0; 5955 0; 6290 0; 6644 0; 7018 0; 7414 0; 7831 0; 8272 0; 8738 0; 9230 0; 9749 0; 10298 0; 10878 0; 11490 0; 12137 0; 12821 0; 13543 0; 14305 0; 15110 0; 15961 0; 16860 0; 17809 0; 18812 0; 19871 0
```

格式说明：
- 以`GraphicEQ:`开头
- 频率和增益值用空格分隔
- 不同频段用分号`;`分隔
- 频率单位为Hz，增益单位为dB

## 技术实现

### 核心技术
- **DynamicsProcessing API**: 使用Android原生的DynamicsProcessing类实现多频段均衡
- **Session 0**: 将音频效果附加到全局音频会话(Session 0)，影响所有应用的音频输出
- **Jetpack Compose**: 使用现代化的声明式UI框架构建界面
- **Material Design 3**: 遵循最新的Material Design设计规范

### 架构设计
- **MVVM架构**: 使用ViewModel管理UI状态和业务逻辑
- **单一数据源**: 使用StateFlow管理应用状态
- **模块化设计**: 将功能分解为独立的模块和组件

## 注意事项

1. **设备兼容性**: 不是所有Android 9.0+设备都支持DynamicsProcessing API，应用会自动检测并提示
2. **音频质量**: 过度的EQ调节可能会影响音频质量，建议使用专业的AutoEq配置文件
3. **电池消耗**: 全局音频处理可能会增加电池消耗
4. **系统重启**: 系统重启后需要重新应用EQ效果

## 开发信息

- **开发语言**: Kotlin
- **最低SDK**: 28 (Android 9.0)
- **目标SDK**: 36
- **构建工具**: Gradle 8.0+

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 免责声明

本应用仅供学习和研究使用。使用时请注意保护听力，避免过高的音量和不当的EQ设置。
