package cn.ykload.flowmix.file

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.DocumentsContract
import android.provider.MediaStore

/**
 * 文件访问助手
 * 提供多种文件选择方式以适应不同的Android版本和权限情况
 */
class FileAccessHelper(private val context: Context) {
    
    companion object {
        private const val MIME_TYPE_TEXT = "text/plain"
        private const val MIME_TYPE_ALL_TEXT = "text/*"
        private const val MIME_TYPE_ALL = "*/*"
    }
    
    /**
     * 创建文件选择Intent
     * 根据Android版本和权限情况选择最合适的方式
     */
    fun createFilePickerIntent(): Intent {
        return when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT -> {
                // Android 4.4+ 使用Storage Access Framework
                createSAFIntent()
            }
            else -> {
                // 旧版本使用传统文件选择器
                createLegacyIntent()
            }
        }
    }
    
    /**
     * 创建Storage Access Framework Intent
     */
    private fun createSAFIntent(): Intent {
        val intent = Intent(Intent.ACTION_OPEN_DOCUMENT).apply {
            addCategory(Intent.CATEGORY_OPENABLE)
            type = MIME_TYPE_ALL_TEXT
            
            // 添加额外的MIME类型
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                putExtra(Intent.EXTRA_MIME_TYPES, arrayOf(
                    MIME_TYPE_TEXT,
                    MIME_TYPE_ALL_TEXT,
                    "application/octet-stream" // 处理没有扩展名的文件
                ))
            }
            
            // 设置初始目录（如果支持）
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                putExtra(DocumentsContract.EXTRA_INITIAL_URI, getInitialUri())
            }
        }
        
        return intent
    }
    
    /**
     * 创建传统文件选择Intent
     */
    private fun createLegacyIntent(): Intent {
        val intent = Intent(Intent.ACTION_GET_CONTENT).apply {
            type = MIME_TYPE_ALL_TEXT
            addCategory(Intent.CATEGORY_OPENABLE)
        }
        
        return Intent.createChooser(intent, "选择AutoEq文件或直接开始调节吧~")
    }
    
    /**
     * 获取初始URI（下载目录）
     */
    private fun getInitialUri(): Uri? {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                MediaStore.Downloads.EXTERNAL_CONTENT_URI
            } else {
                null
            }
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 验证选择的文件是否有效
     */
    fun validateSelectedFile(uri: Uri): FileValidationResult {
        return try {
            val contentResolver = context.contentResolver
            
            // 检查URI是否可访问
            contentResolver.query(uri, null, null, null, null)?.use { cursor ->
                if (!cursor.moveToFirst()) {
                    return FileValidationResult.Error("无法访问选择的文件")
                }
            }
            
            // 检查文件类型
            val mimeType = contentResolver.getType(uri)
            val fileName = getFileName(uri)
            
            when {
                fileName?.endsWith(".txt", ignoreCase = true) == true -> {
                    FileValidationResult.Success
                }
                mimeType?.startsWith("text/") == true -> {
                    FileValidationResult.Success
                }
                else -> {
                    FileValidationResult.Warning("文件类型可能不正确，建议选择.txt文件")
                }
            }
        } catch (e: Exception) {
            FileValidationResult.Error("文件验证失败: ${e.message}")
        }
    }
    
    /**
     * 从URI获取文件名
     */
    private fun getFileName(uri: Uri): String? {
        return try {
            context.contentResolver.query(uri, null, null, null, null)?.use { cursor ->
                val nameIndex = cursor.getColumnIndex(android.provider.OpenableColumns.DISPLAY_NAME)
                if (nameIndex != -1 && cursor.moveToFirst()) {
                    cursor.getString(nameIndex)
                } else {
                    uri.lastPathSegment
                }
            }
        } catch (e: Exception) {
            uri.lastPathSegment
        }
    }
    
    /**
     * 获取文件大小
     */
    fun getFileSize(uri: Uri): Long {
        return try {
            context.contentResolver.query(uri, null, null, null, null)?.use { cursor ->
                val sizeIndex = cursor.getColumnIndex(android.provider.OpenableColumns.SIZE)
                if (sizeIndex != -1 && cursor.moveToFirst()) {
                    cursor.getLong(sizeIndex)
                } else {
                    -1L
                }
            } ?: -1L
        } catch (e: Exception) {
            -1L
        }
    }
    
    /**
     * 创建多种文件选择方式的Intent列表
     */
    fun createMultipleFilePickerIntents(): List<Intent> {
        val intents = mutableListOf<Intent>()
        
        // 主要的文件选择器
        intents.add(createFilePickerIntent())
        
        // 备用的文件管理器
        try {
            val fileManagerIntent = Intent(Intent.ACTION_GET_CONTENT).apply {
                type = "*/*"
                addCategory(Intent.CATEGORY_OPENABLE)
            }
            intents.add(fileManagerIntent)
        } catch (e: Exception) {
            // 忽略错误
        }
        
        // 文档选择器
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            try {
                val documentsIntent = Intent(Intent.ACTION_OPEN_DOCUMENT).apply {
                    addCategory(Intent.CATEGORY_OPENABLE)
                    type = "*/*"
                }
                intents.add(documentsIntent)
            } catch (e: Exception) {
                // 忽略错误
            }
        }
        
        return intents
    }
}

/**
 * 文件验证结果
 */
sealed class FileValidationResult {
    object Success : FileValidationResult()
    data class Warning(val message: String) : FileValidationResult()
    data class Error(val message: String) : FileValidationResult()
}
