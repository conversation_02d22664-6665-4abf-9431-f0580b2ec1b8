package cn.ykload.flowmix.utils

/**
 * 应用常量
 */
object Constants {
    
    // 音频相关常量
    const val GLOBAL_AUDIO_SESSION_ID = 0
    const val MIN_ANDROID_VERSION_FOR_DYNAMICS_PROCESSING = 28 // Android 9.0
    
    // 文件相关常量
    const val MAX_FILE_SIZE_MB = 10
    const val MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024
    
    // EQ相关常量
    const val MIN_FREQUENCY_HZ = 10f
    const val MAX_FREQUENCY_HZ = 24000f
    const val MIN_GAIN_DB = -30f
    const val MAX_GAIN_DB = 30f
    const val MAX_EQ_BANDS = 1000
    
    // 错误消息
    object ErrorMessages {
        const val DEVICE_NOT_SUPPORTED = "设备不支持DynamicsProcessing API (需要Android 9.0+)，若Wavelet正在运行，请关闭Wavelet"
        const val PERMISSION_DENIED = "权限被拒绝，无法正常使用应用功能"
        const val FILE_TOO_LARGE = "文件过大，请选择小于${MAX_FILE_SIZE_MB}MB的文件"
        const val INVALID_FILE_FORMAT = "无效的AutoEq文件格式"
        const val FILE_READ_ERROR = "文件读取失败"
        const val AUDIO_EFFECT_APPLY_ERROR = "应用音频效果失败"
        const val AUDIO_EFFECT_TOGGLE_ERROR = "切换音频效果状态失败"
        const val NO_EQ_DATA = "请先选择并导入AutoEq文件"
        const val EMPTY_FILE = "文件为空或无有效数据"
        const val TOO_MANY_BANDS = "EQ频段数量过多 (最大支持${MAX_EQ_BANDS}个频段)"
    }
    
    // 成功消息
    object SuccessMessages {
        const val EQ_APPLIED = "AutoEq已成功应用"
        const val EQ_ENABLED = "音频效果已启用"
        const val EQ_DISABLED = "音频效果已禁用"
        const val FILE_IMPORTED = "文件导入成功"
    }
    
    // 支持的文件类型
    val SUPPORTED_MIME_TYPES = arrayOf(
        "text/plain",
        "text/*",
        "*/*"
    )
    
    // AutoEq文件格式相关
    object AutoEqFormat {
        const val GRAPHIC_EQ_PREFIX = "GraphicEQ:"
        const val BAND_SEPARATOR = ";"
        const val FREQ_GAIN_SEPARATOR = " "
        
        // 标准AutoEq频率点 (Hz)
        val STANDARD_FREQUENCIES = arrayOf(
            20f, 21f, 22f, 23f, 24f, 26f, 27f, 29f, 30f, 32f, 34f, 36f, 38f, 40f, 43f, 45f, 48f, 50f,
            53f, 56f, 59f, 63f, 66f, 70f, 74f, 78f, 83f, 87f, 92f, 97f, 103f, 109f, 115f, 121f, 128f,
            136f, 143f, 151f, 160f, 169f, 178f, 188f, 199f, 210f, 222f, 235f, 248f, 262f, 277f, 292f,
            309f, 326f, 345f, 364f, 385f, 406f, 429f, 453f, 479f, 506f, 534f, 565f, 596f, 630f, 665f,
            703f, 743f, 784f, 829f, 875f, 924f, 977f, 1032f, 1090f, 1151f, 1216f, 1284f, 1357f, 1433f,
            1514f, 1599f, 1689f, 1784f, 1885f, 1991f, 2103f, 2221f, 2347f, 2479f, 2618f, 2766f, 2921f,
            3086f, 3260f, 3443f, 3637f, 3842f, 4058f, 4287f, 4528f, 4783f, 5052f, 5337f, 5637f, 5955f,
            6290f, 6644f, 7018f, 7414f, 7831f, 8272f, 8738f, 9230f, 9749f, 10298f, 10878f, 11490f,
            12137f, 12821f, 13543f, 14305f, 15110f, 15961f, 16860f, 17809f, 18812f, 19871f
        )
    }
}
