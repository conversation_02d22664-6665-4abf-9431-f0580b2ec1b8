@echo off
echo ========================================
echo FlowMix Release Build Fix Test Script
echo ========================================
echo.

echo [1/6] Cleaning project...
call gradlew clean
if %errorlevel% neq 0 (
    echo ERROR: Clean failed
    pause
    exit /b 1
)
echo Clean completed successfully.
echo.

echo [2/6] Building release APK...
call gradlew assembleRelease
if %errorlevel% neq 0 (
    echo ERROR: Release build failed
    pause
    exit /b 1
)
echo Release build completed successfully.
echo.

echo [3/6] Checking output files...
if exist "app\build\outputs\apk\release\app-release.apk" (
    echo ✓ APK file generated successfully
) else (
    echo ✗ APK file not found
    pause
    exit /b 1
)

if exist "app\build\outputs\mapping\release\mapping.txt" (
    echo ✓ ProGuard mapping file generated
) else (
    echo ✗ ProGuard mapping file not found
)
echo.

echo [4/6] Checking ProGuard mapping for critical classes...
findstr /C:"WebSocketMessage" "app\build\outputs\mapping\release\mapping.txt" >nul
if %errorlevel% equ 0 (
    echo ✓ WebSocketMessage found in mapping
) else (
    echo ⚠ WebSocketMessage not found in mapping - this might be OK if it's kept
)

findstr /C:"DataSourcesResponse" "app\build\outputs\mapping\release\mapping.txt" >nul
if %errorlevel% equ 0 (
    echo ✓ DataSourcesResponse found in mapping
) else (
    echo ⚠ DataSourcesResponse not found in mapping - this might be OK if it's kept
)
echo.

echo [5/6] APK Information:
for %%I in ("app\build\outputs\apk\release\app-release.apk") do (
    echo File size: %%~zI bytes
    echo File path: %%~fI
)
echo.

echo [6/6] Testing checklist:
echo.
echo After installing the APK, please test:
echo 1. ✓ 数据源列表加载 (频响页面)
echo 2. ✓ FlowSync 登录功能
echo 3. ✓ WebSocket 连接建立
echo 4. ✓ 云端同步功能
echo 5. ✓ 配置保存和加载
echo.
echo Expected fixes:
echo - No more "java.lang.Class cannot be cast to java.lang.reflect.ParameterizedType" errors
echo - FlowSync WebSocket connection works properly
echo - Message serialization/deserialization works correctly
echo.

echo ========================================
echo Build completed successfully!
echo Install command: adb install app\build\outputs\apk\release\app-release.apk
echo ========================================
pause
