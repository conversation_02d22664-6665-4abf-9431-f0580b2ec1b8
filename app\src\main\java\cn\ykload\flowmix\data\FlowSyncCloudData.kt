package cn.ykload.flowmix.data

import com.google.gson.annotations.SerializedName

/**
 * FlowSync 云端同步相关数据类
 */

/**
 * 登录请求数据
 */
data class LoginRequest(
    @SerializedName("token")
    val token: String = "Flowmix.FlowSync777-AkkoAndYKload233",
    @SerializedName("loginCode")
    val loginCode: String
)

/**
 * 登录响应数据
 */
data class LoginResponse(
    @SerializedName("success")
    val success: Bo<PERSON>an,
    @SerializedName("authToken")
    val authToken: String? = null,
    @SerializedName("qq")
    val qq: String? = null,
    @SerializedName("error")
    val error: String? = null,
    @SerializedName("message")
    val message: String? = null
)

/**
 * 用户认证信息
 */
data class AuthInfo(
    @SerializedName("authToken")
    val authToken: String,
    @SerializedName("qq")
    val qq: String,
    @SerializedName("createdAt")
    val createdAt: Long = System.currentTimeMillis()
)

/**
 * WebSocket 消息基类
 */
sealed class WebSocketMessage {
    abstract val type: String
}

/**
 * 认证消息
 */
data class AuthMessage(
    override val type: String = "auth",
    @SerializedName("authToken")
    val authToken: String,
    @SerializedName("clientInfo")
    val clientInfo: ClientInfo
) : WebSocketMessage()

/**
 * 客户端信息
 */
data class ClientInfo(
    @SerializedName("platform")
    val platform: String = "android",
    @SerializedName("version")
    val version: String,
    @SerializedName("deviceId")
    val deviceId: String
)

/**
 * 认证成功响应
 */
data class AuthSuccessMessage(
    override val type: String = "auth_success",
    @SerializedName("qq")
    val qq: String,
    @SerializedName("message")
    val message: String
) : WebSocketMessage()

/**
 * 认证失败响应
 */
data class AuthFailedMessage(
    override val type: String = "auth_failed",
    @SerializedName("error")
    val error: String,
    @SerializedName("message")
    val message: String
) : WebSocketMessage()

/**
 * 获取云端配置请求
 */
data class GetCloudConfigMessage(
    override val type: String = "get_cloud_config"
) : WebSocketMessage()

/**
 * 云端配置响应
 */
data class CloudConfigMessage(
    override val type: String = "cloud_config",
    @SerializedName("data")
    val data: CloudDeviceConfigCollection
) : WebSocketMessage()

/**
 * 同步到云端请求
 */
data class SyncToCloudMessage(
    override val type: String = "sync_to_cloud",
    @SerializedName("data")
    val data: CloudDeviceConfigCollection,
    @SerializedName("deviceId")
    val deviceId: String
) : WebSocketMessage()

/**
 * 同步成功响应
 */
data class SyncSuccessMessage(
    override val type: String = "sync_success",
    @SerializedName("syncedAt")
    val syncedAt: Long,
    @SerializedName("message")
    val message: String
) : WebSocketMessage()

/**
 * 同步失败响应
 */
data class SyncFailedMessage(
    override val type: String = "sync_failed",
    @SerializedName("message")
    val message: String
) : WebSocketMessage()

/**
 * 配置更新通知
 */
data class ConfigUpdatedMessage(
    override val type: String = "config_updated",
    @SerializedName("data")
    val data: ConfigUpdateData
) : WebSocketMessage()

/**
 * 配置更新数据
 */
data class ConfigUpdateData(
    @SerializedName("deviceId")
    val deviceId: String,
    @SerializedName("config")
    val config: DeviceConfig,
    @SerializedName("version")
    val version: Int,
    @SerializedName("updatedBy")
    val updatedBy: String
)

/**
 * 心跳消息
 */
data class PingMessage(
    override val type: String = "ping"
) : WebSocketMessage()

/**
 * 心跳响应
 */
data class PongMessage(
    override val type: String = "pong"
) : WebSocketMessage()

/**
 * 错误消息
 */
data class ErrorMessage(
    override val type: String = "error",
    @SerializedName("error")
    val error: String,
    @SerializedName("message")
    val message: String
) : WebSocketMessage()

/**
 * 云端设备配置集合
 * 与本地的 DeviceConfigCollection 类似，但包含QQ号
 * 使用时间戳而不是版本号进行同步控制
 * 注意：目标曲线数据已从FrequencyResponseConfig中移除，不再参与云端同步
 */
data class CloudDeviceConfigCollection(
    @SerializedName("qq")
    val qq: String,
    @SerializedName("configs")
    val configs: Map<String, DeviceConfig> = emptyMap(),
    @SerializedName("lastUpdated")
    val lastUpdated: Long = System.currentTimeMillis()
) {
    /**
     * 转换为本地配置集合
     */
    fun toLocalCollection(): DeviceConfigCollection {
        return DeviceConfigCollection(
            configs = configs,
            lastUpdated = lastUpdated
        )
    }

    /**
     * 从本地配置集合创建云端配置集合
     */
    companion object {
        fun fromLocalCollection(
            qq: String,
            localCollection: DeviceConfigCollection
        ): CloudDeviceConfigCollection {
            return CloudDeviceConfigCollection(
                qq = qq,
                configs = localCollection.configs,
                lastUpdated = localCollection.lastUpdated
            )
        }
    }
}

/**
 * WebSocket 连接状态
 */
enum class WebSocketState {
    DISCONNECTED,    // 未连接
    CONNECTING,      // 连接中
    CONNECTED,       // 已连接但未认证
    AUTHENTICATED,   // 已认证
    RECONNECTING,    // 重连中
    ERROR           // 连接错误
}

/**
 * 同步状态
 */
enum class CloudSyncStatus {
    IDLE,           // 空闲
    CONNECTING,     // 连接中
    SYNCING,        // 同步中
    SYNCED,         // 已同步
    ERROR,          // 错误
    OFFLINE         // 离线
}
