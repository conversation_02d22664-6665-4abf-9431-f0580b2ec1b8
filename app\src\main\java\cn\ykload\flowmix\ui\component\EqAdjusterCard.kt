package cn.ykload.flowmix.ui.component

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Remove
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import cn.ykload.flowmix.data.AutoEqData
import cn.ykload.flowmix.data.EqBand
import kotlinx.coroutines.delay

/**
 * EQ调节器Card组件
 * 显示可横向滑动的频段调节UI
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EqAdjusterCard(
    eqData: AutoEqData?,
    isExpanded: Boolean,
    onAdjustBand: (Float, Float) -> Unit,
    onSetBandGain: (Float, Float) -> Unit = { _, _ -> },
    onVisibleRangeChanged: (IntRange?) -> Unit = {},
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .animateContentSize(
                animationSpec = spring(
                    dampingRatio = Spring.DampingRatioMediumBouncy,
                    stiffness = Spring.StiffnessMedium
                )
            ),
        shape = RoundedCornerShape(25.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        AnimatedVisibility(
            visible = isExpanded,
            enter = fadeIn(animationSpec = tween(300)),
            exit = fadeOut(animationSpec = tween(200))
        ) {
            if (eqData != null && eqData.bands.isNotEmpty()) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 12.dp)
                ) {
                    
                    
                    // 横向滑动的频段调节器
                    val lazyListState = rememberLazyListState()

                    // 监听滚动状态，报告可见范围
                    LaunchedEffect(lazyListState.firstVisibleItemIndex, lazyListState.firstVisibleItemScrollOffset) {
                        val layoutInfo = lazyListState.layoutInfo
                        if (layoutInfo.visibleItemsInfo.isNotEmpty()) {
                            val firstVisibleIndex = layoutInfo.visibleItemsInfo.first().index
                            val lastVisibleIndex = layoutInfo.visibleItemsInfo.last().index
                            onVisibleRangeChanged(firstVisibleIndex..lastVisibleIndex)
                        } else {
                            onVisibleRangeChanged(null)
                        }
                    }

                    LazyRow(
                        state = lazyListState,
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        contentPadding = PaddingValues(horizontal = 4.dp),
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        itemsIndexed(eqData.bands) { index, band ->
                            EqBandAdjuster(
                                band = band,
                                allBands = eqData.bands,
                                lazyListState = lazyListState,
                                onAdjust = { deltaGain ->
                                    onAdjustBand(band.frequency, deltaGain)
                                },
                                onSetGain = { newGain ->
                                    onSetBandGain(band.frequency, newGain)
                                }
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 单个频段调节器
 */
@Composable
private fun EqBandAdjuster(
    band: EqBand,
    allBands: List<EqBand>,
    lazyListState: androidx.compose.foundation.lazy.LazyListState,
    onAdjust: (Float) -> Unit,
    onSetGain: (Float) -> Unit = {},
    modifier: Modifier = Modifier
) {
    // 显示增益输入对话框的状态
    var showGainInputDialog by remember { mutableStateOf(false) }
    Column(
        modifier = modifier.width(50.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(2.dp)
    ) {
        // 频段名称
        Text(
            text = formatFrequency(band.frequency),
            style = MaterialTheme.typography.bodySmall,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.Center,
            maxLines = 1
        )

        // 增益数值 - 计算透明度并添加点击功能
        val gainAlpha = calculateRelativeGainAlpha(band, allBands, lazyListState)
        Text(
            text = "${if (band.gain >= 0) "+" else ""}${"%.1f".format(band.gain)}dB",
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary.copy(alpha = gainAlpha),
            textAlign = TextAlign.Center,
            modifier = Modifier.clickable {
                showGainInputDialog = true
            }
        )
        
        // 加号按钮
        EqAdjustButton(
            icon = Icons.Default.Add,
            onAdjust = { onAdjust(0.1f) },
            contentDescription = "增加增益"
        )
        
        // 减号按钮
        EqAdjustButton(
            icon = Icons.Default.Remove,
            onAdjust = { onAdjust(-0.1f) },
            contentDescription = "减少增益"
        )
    }

    // 增益输入对话框
    if (showGainInputDialog) {
        GainInputDialog(
            currentGain = band.gain,
            frequency = band.frequency,
            onConfirm = { newGain ->
                onSetGain(newGain)
                showGainInputDialog = false
            },
            onDismiss = {
                showGainInputDialog = false
            }
        )
    }
}

/**
 * EQ调节按钮（支持长按连续调节）
 */
@Composable
private fun EqAdjustButton(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    onAdjust: () -> Unit,
    contentDescription: String,
    modifier: Modifier = Modifier
) {
    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()
    
    // 长按连续调节
    LaunchedEffect(isPressed) {
        if (isPressed) {
            delay(500) // 初始延迟500ms
            while (isPressed) {
                onAdjust()
                delay(200) // 每200ms调节一次
            }
        }
    }
    
    // 按钮缩放动画
    val scale by animateFloatAsState(
        targetValue = if (isPressed) 0.9f else 1f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessHigh
        ),
        label = "button_scale"
    )
    
    IconButton(
        onClick = onAdjust,
        interactionSource = interactionSource,
        modifier = modifier
            .size(32.dp)
            .clip(CircleShape)
            .background(MaterialTheme.colorScheme.primary.copy(alpha = 0.1f))
            .graphicsLayer {
                scaleX = scale
                scaleY = scale
            }
    ) {
        Icon(
            imageVector = icon,
            contentDescription = contentDescription,
            tint = MaterialTheme.colorScheme.primary,
            modifier = Modifier.size(16.dp)
        )
    }
}

/**
 * 格式化频率显示
 */
private fun formatFrequency(frequency: Float): String {
    return when {
        frequency >= 1000f -> {
            val kHz = frequency / 1000f
            if (kHz == kHz.toInt().toFloat()) {
                "${kHz.toInt()}k"
            } else {
                "${"%.1f".format(kHz)}k"
            }
        }
        else -> "${frequency.toInt()}"
    }
}

/**
 * 根据当前可见频段的相对增益幅度计算透明度
 * 基于可见范围内的最大和最小增益值进行相对计算
 */
@Composable
private fun calculateRelativeGainAlpha(
    currentBand: EqBand,
    allBands: List<EqBand>,
    lazyListState: androidx.compose.foundation.lazy.LazyListState
): Float {
    // 获取当前可见的频段范围
    val layoutInfo = lazyListState.layoutInfo
    val visibleBands = if (layoutInfo.visibleItemsInfo.isNotEmpty()) {
        val firstVisibleIndex = layoutInfo.visibleItemsInfo.first().index
        val lastVisibleIndex = layoutInfo.visibleItemsInfo.last().index
        allBands.subList(
            firstVisibleIndex.coerceAtLeast(0),
            (lastVisibleIndex + 1).coerceAtMost(allBands.size)
        )
    } else {
        allBands
    }

    // 如果可见频段少于2个，使用默认透明度
    if (visibleBands.size < 2) {
        return 0.7f
    }

    // 计算可见频段的增益范围
    val visibleGains = visibleBands.map { kotlin.math.abs(it.gain) }
    val minVisibleGain = visibleGains.minOrNull() ?: 0f
    val maxVisibleGain = visibleGains.maxOrNull() ?: 0f

    // 如果所有可见频段的增益都相同，使用默认透明度
    val gainRange = maxVisibleGain - minVisibleGain
    if (gainRange < 0.1f) {
        return 0.7f
    }

    // 计算当前频段在可见范围内的相对位置
    val currentAbsGain = kotlin.math.abs(currentBand.gain)
    val normalizedPosition = ((currentAbsGain - minVisibleGain) / gainRange).coerceIn(0f, 1f)

    // 透明度范围：最小0.3，最大1.0
    val minAlpha = 0.3f
    val maxAlpha = 1.0f

    return minAlpha + (maxAlpha - minAlpha) * normalizedPosition
}

/**
 * 增益输入对话框
 */
@Composable
private fun GainInputDialog(
    currentGain: Float,
    frequency: Float,
    onConfirm: (Float) -> Unit,
    onDismiss: () -> Unit
) {
    var inputText by remember { mutableStateOf("%.1f".format(currentGain)) }
    var isError by remember { mutableStateOf(false) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "设置增益值",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium
            )
        },
        text = {
            Column(
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Text(
                    text = "频段: ${formatFrequency(frequency)}Hz",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )

                OutlinedTextField(
                    value = inputText,
                    onValueChange = { newValue ->
                        inputText = newValue
                        // 验证输入
                        isError = try {
                            val gain = newValue.toFloat()
                            gain < -30f || gain > 30f
                        } catch (e: NumberFormatException) {
                            true
                        }
                    },
                    label = { Text("增益值 (dB)") },
                    placeholder = { Text("请输入-30到30之间的数值") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                    singleLine = true,
                    isError = isError,
                    supportingText = if (isError) {
                        { Text("请输入-30到30之间的有效数值") }
                    } else null,
                    shape = RoundedCornerShape(25.dp),
                    modifier = Modifier.fillMaxWidth()
                )
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    try {
                        val gain = inputText.toFloat().coerceIn(-30f, 30f)
                        onConfirm(gain)
                    } catch (e: NumberFormatException) {
                        // 输入无效，不执行任何操作
                    }
                },
                enabled = !isError && inputText.isNotBlank()
            ) {
                Text("确定")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}
