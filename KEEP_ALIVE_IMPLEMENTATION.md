# Flowmix 保活机制实现文档

## 概述

为Flowmix应用实现了完整的保活机制，确保应用在后台持续运行，维持FlowSync和AutoEq功能的正常工作。保活功能始终启用，无需用户配置。

## 核心组件

### 1. FlowmixKeepAliveService (前台服务)
- **位置**: `app/src/main/java/cn/ykload/flowmix/service/FlowmixKeepAliveService.kt`
- **功能**:
  - 通过前台服务机制保持应用在后台运行
  - 维持FlowSync设备监听功能
  - 保持AutoEq音频效果处理能力
  - 显示动态状态通知

### 2. NotificationHelper (通知管理)
- **位置**: `app/src/main/java/cn/ykload/flowmix/notification/NotificationHelper.kt`
- **功能**:
  - 管理保活服务的常驻通知
  - 根据Flowmix总开关状态显示不同内容（主标题：Flowmix，副标题：On/Off）
  - 通知不可被用户清除（ongoing通知）
  - 点击通知可打开应用

### 3. ServiceManager (服务管理器)
- **位置**: `app/src/main/java/cn/ykload/flowmix/service/ServiceManager.kt`
- **功能**:
  - 统一管理前台服务的启动、停止和状态监控
  - 与UI层的状态同步
  - 服务健康状态检查
  - 保活功能始终启用，无用户配置选项

### 4. BootReceiver (开机自启动)
- **位置**: `app/src/main/java/cn/ykload/flowmix/receiver/BootReceiver.kt`
- **功能**:
  - 设备开机后自动启动保活服务
  - 应用更新后重启服务
  - 延迟启动避免系统资源冲突

## 权限配置

在 `AndroidManifest.xml` 中添加了以下权限：

```xml
<!-- 前台服务权限 -->
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
<uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />

<!-- 通知权限 -->
<uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

<!-- 开机自启动权限 -->
<uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />

<!-- 唤醒锁权限 -->
<uses-permission android:name="android.permission.WAKE_LOCK" />
```

## 用户界面集成

### 权限申请
在欢迎页面（OnboardingScreen）中添加了通知权限的申请：
- **通知权限**: 用于显示应用状态和保活通知
- **Android 13+**: 需要用户明确授权通知权限
- **自动集成**: 与其他权限一起在引导流程中申请

### 常驻通知
- **主标题**: "Flowmix"
- **副标题**: "On" 或 "Off"（根据总开关状态）
- **不可清除**: 设置为ongoing通知，用户无法滑动删除
- **点击打开**: 点击通知可直接打开应用

## 工作流程

### 1. 应用启动
1. MainActivity初始化ServiceManager
2. 检查保活服务设置
3. 如果启用且有权限，自动启动保活服务

### 2. 服务运行
1. 前台服务创建常驻通知
2. 初始化FlowSync组件
3. 每30秒检查服务健康状态
4. 确保FlowSync设备监听保持活跃

### 3. 状态同步
1. MainViewModel监听Flowmix状态变化
2. 自动更新服务通知内容
3. 同步UI状态和服务状态

### 4. 开机自启动
1. BootReceiver接收开机广播
2. 延迟5秒后自动启动保活服务（无需用户配置）
3. 避免开机时系统资源紧张

## 保活策略

### 1. 前台服务
- 使用前台服务确保系统不会轻易杀死应用
- 设置为媒体播放类型，提高优先级
- 返回START_STICKY确保服务被杀死后重启

### 2. 通知常驻
- 创建低优先级通知渠道
- 设置为ongoing通知，用户无法滑动删除
- 提供有用的状态信息和快捷操作

### 3. 健康检查
- 定期检查服务状态
- 自动重启异常停止的服务
- 确保FlowSync功能持续运行

### 4. 自动化运行
- 保活功能始终启用，无需用户配置
- 应用启动时自动启动保活服务
- 开机后自动恢复保活状态

## 使用方法

### 权限授权
1. 首次启动应用时，在欢迎页面授权所需权限
2. 包括音频、蓝牙、存储和通知权限
3. 通知权限用于显示保活状态

### 通知管理
- 保活服务启动后会显示常驻通知
- 通知标题：Flowmix，副标题：On/Off
- 通知不可被用户清除
- 点击通知可快速打开应用

### 自动运行
- 保活服务在应用启动时自动启动
- 设备重启后自动恢复运行
- 无需用户手动配置

## 注意事项

### 1. 权限要求
- Android 13+需要通知权限
- 某些厂商可能需要手动设置自启动权限
- 建议在应用设置中允许后台运行

### 2. 电池优化
- 部分设备可能需要关闭电池优化
- 建议将应用加入白名单
- 避免被系统的省电模式影响

### 3. 内存管理
- 服务会占用一定的内存资源
- 在低内存设备上可能影响性能
- 用户可根据需要关闭保活功能

## 技术特点

1. **模块化设计**: 各组件职责清晰，易于维护
2. **状态同步**: UI和服务状态实时同步
3. **用户友好**: 简洁的通知界面，无需复杂配置
4. **资源优化**: 合理使用系统资源，避免过度消耗
5. **兼容性好**: 支持Android 9.0+，适配不同版本
6. **自动化**: 保活功能完全自动化，无需用户干预

## 测试建议

1. **权限申请测试**:
   - 验证欢迎页面通知权限申请
   - 测试权限拒绝和重新申请流程
   - 确认所有权限正确授权

2. **保活功能测试**:
   - 检查应用启动时服务自动启动
   - 验证通知显示和状态更新
   - 测试开机自启动功能

3. **后台运行测试**:
   - 切换到其他应用
   - 清理最近任务
   - 验证FlowSync功能是否持续工作

4. **通知系统测试**:
   - 切换Flowmix开关
   - 检查通知副标题更新（On/Off）
   - 验证通知不可被清除
   - 测试点击通知打开应用

5. **异常恢复测试**:
   - 强制停止应用
   - 模拟内存不足情况
   - 验证服务自动重启功能
