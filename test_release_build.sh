#!/bin/bash

echo "========================================"
echo "FlowMix Release Build Test Script"
echo "========================================"
echo

echo "[1/5] Cleaning project..."
./gradlew clean
if [ $? -ne 0 ]; then
    echo "ERROR: Clean failed"
    exit 1
fi
echo "Clean completed successfully."
echo

echo "[2/5] Building release APK..."
./gradlew assembleRelease
if [ $? -ne 0 ]; then
    echo "ERROR: Release build failed"
    exit 1
fi
echo "Release build completed successfully."
echo

echo "[3/5] Checking output files..."
if [ -f "app/build/outputs/apk/release/app-release.apk" ]; then
    echo "✓ APK file generated successfully"
else
    echo "✗ APK file not found"
    exit 1
fi

if [ -f "app/build/outputs/mapping/release/mapping.txt" ]; then
    echo "✓ ProGuard mapping file generated"
else
    echo "✗ ProGuard mapping file not found"
fi
echo

echo "[4/5] APK Information:"
APK_PATH="app/build/outputs/apk/release/app-release.apk"
if [ -f "$APK_PATH" ]; then
    APK_SIZE=$(stat -f%z "$APK_PATH" 2>/dev/null || stat -c%s "$APK_PATH" 2>/dev/null)
    echo "File size: $APK_SIZE bytes"
    echo "File path: $(realpath "$APK_PATH")"
fi
echo

echo "[5/5] Next steps:"
echo "1. Install the APK: adb install app/build/outputs/apk/release/app-release.apk"
echo "2. Test data source loading functionality"
echo "3. Check for the ParameterizedType error"
echo

echo "========================================"
echo "Build completed successfully!"
echo "========================================"
