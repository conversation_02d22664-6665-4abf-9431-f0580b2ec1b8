package cn.ykload.flowmix.data

/**
 * 表示单个EQ频段的数据
 * @param frequency 频率 (Hz)
 * @param gain 增益 (dB)
 */
data class EqBand(
    val frequency: Float,
    val gain: Float
)

/**
 * AutoEq配置数据
 * @param bands EQ频段列表
 * @param name 配置名称
 */
data class AutoEqData(
    val bands: List<EqBand>,
    val name: String = "AutoEq"
) {
    /**
     * 验证数据是否有效
     */
    fun isValid(): Boolean {
        return bands.isNotEmpty() && bands.all { 
            it.frequency > 0 && it.gain >= -30 && it.gain <= 30 
        }
    }
    
    /**
     * 获取频率范围
     */
    fun getFrequencyRange(): Pair<Float, Float> {
        if (bands.isEmpty()) return Pair(0f, 0f)
        return Pair(bands.minOf { it.frequency }, bands.maxOf { it.frequency })
    }
    
    /**
     * 获取增益范围
     */
    fun getGainRange(): Pair<Float, Float> {
        if (bands.isEmpty()) return Pair(0f, 0f)
        return Pair(bands.minOf { it.gain }, bands.maxOf { it.gain })
    }

    /**
     * 计算等响度补偿值
     * 使用A-weighting曲线近似计算响度补偿
     * @return 需要应用的整体增益补偿值 (dB)
     */
    fun calculateLoudnessCompensation(): Float {
        if (bands.isEmpty()) return 0f

        // 计算加权平均增益，重点关注人耳敏感频段
        var weightedGainSum = 0f
        var totalWeight = 0f

        bands.forEach { band ->
            val weight = getAWeightingFactor(band.frequency)
            weightedGainSum += band.gain * weight
            totalWeight += weight
        }

        // 返回负的加权平均增益作为补偿值
        return if (totalWeight > 0) -(weightedGainSum / totalWeight) else 0f
    }

    /**
     * 获取A-weighting权重因子
     * 基于ISO 226标准的A-weighting曲线
     */
    private fun getAWeightingFactor(frequency: Float): Float {
        // A-weighting近似公式
        val f = frequency.toDouble()
        val f2 = f * f
        val f4 = f2 * f2

        val numerator = 12194.0 * 12194.0 * f4
        val denominator = (f2 + 20.6 * 20.6) *
                         kotlin.math.sqrt((f2 + 107.7 * 107.7) * (f2 + 737.9 * 737.9)) *
                         (f2 + 12194.0 * 12194.0)

        val aWeighting = numerator / denominator

        // 转换为线性权重因子，1kHz处为1.0
        return (aWeighting / 1.0).toFloat().coerceIn(0.1f, 2.0f)
    }

    /**
     * 应用等响度补偿，返回新的AutoEqData
     * @param compensation 补偿值 (dB)
     * @return 应用补偿后的新AutoEqData
     */
    fun withLoudnessCompensation(compensation: Float): AutoEqData {
        if (compensation == 0f) return this

        val compensatedBands = bands.map { band ->
            EqBand(
                frequency = band.frequency,
                gain = (band.gain + compensation).coerceIn(-30f, 30f)
            )
        }

        return copy(bands = compensatedBands)
    }

    /**
     * 应用整体增益调节，返回新的AutoEqData
     * @param globalGain 整体增益值 (dB)
     * @return 应用整体增益后的新AutoEqData
     */
    fun withGlobalGain(globalGain: Float): AutoEqData {
        if (globalGain == 0f) return this

        val adjustedBands = bands.map { band ->
            EqBand(
                frequency = band.frequency,
                gain = (band.gain + globalGain).coerceIn(-30f, 30f)
            )
        }

        return copy(bands = adjustedBands)
    }

    /**
     * 检查两个AutoEqData是否等效（忽略微小的浮点数差异）
     * @param other 要比较的AutoEqData，可以为null
     * @return 如果两个配置等效则返回true
     */
    fun isEquivalentTo(other: AutoEqData?): Boolean {
        if (other == null) return false
        if (this === other) return true

        // 比较名称
        if (name != other.name) return false

        // 比较频段数量
        if (bands.size != other.bands.size) return false

        // 比较每个频段（允许微小的浮点数差异）
        for (i in bands.indices) {
            val thisBand = bands[i]
            val otherBand = other.bands[i]

            // 频率必须完全相等
            if (thisBand.frequency != otherBand.frequency) return false

            // 增益允许0.01dB的差异
            if (kotlin.math.abs(thisBand.gain - otherBand.gain) > 0.01f) return false
        }

        return true
    }
}
