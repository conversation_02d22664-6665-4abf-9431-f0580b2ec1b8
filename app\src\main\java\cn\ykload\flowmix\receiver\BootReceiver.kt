package cn.ykload.flowmix.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import cn.ykload.flowmix.service.ServiceManager

/**
 * 开机自启动接收器
 * 
 * 在设备开机或应用更新后自动启动保活服务
 */
class BootReceiver : BroadcastReceiver() {

    companion object {
        private const val TAG = "BootReceiver"
    }

    override fun onReceive(context: Context, intent: Intent) {
        Log.d(TAG, "收到广播: ${intent.action}")
        
        when (intent.action) {
            Intent.ACTION_BOOT_COMPLETED -> {
                Log.d(TAG, "设备开机完成，准备启动Flowmix保活服务")
                handleBootCompleted(context)
            }
            Intent.ACTION_MY_PACKAGE_REPLACED,
            Intent.ACTION_PACKAGE_REPLACED -> {
                Log.d(TAG, "应用包已更新，准备重启Flowmix保活服务")
                handlePackageReplaced(context)
            }
        }
    }

    /**
     * 处理开机完成事件
     */
    private fun handleBootCompleted(context: Context) {
        try {
            val serviceManager = ServiceManager.getInstance(context)

            // 保活服务始终启用，开机后自动启动
            Log.d(TAG, "开机完成，启动保活服务")

            // 延迟启动，避免开机时系统资源紧张
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                serviceManager.startKeepAliveService(false) // 默认以关闭状态启动
            }, 5000) // 延迟5秒启动

        } catch (e: Exception) {
            Log.e(TAG, "处理开机完成事件失败", e)
        }
    }

    /**
     * 处理应用包更新事件
     */
    private fun handlePackageReplaced(context: Context) {
        try {
            val serviceManager = ServiceManager.getInstance(context)
            
            // 检查服务健康状态
            serviceManager.checkServiceHealth()
            
            Log.d(TAG, "应用更新后服务状态检查完成")
        } catch (e: Exception) {
            Log.e(TAG, "处理应用更新事件失败", e)
        }
    }
}
