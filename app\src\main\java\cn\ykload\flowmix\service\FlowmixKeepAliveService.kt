package cn.ykload.flowmix.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import android.util.Log
import androidx.core.app.NotificationCompat
import cn.ykload.flowmix.MainActivity
import cn.ykload.flowmix.R
import cn.ykload.flowmix.audio.AudioDeviceManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch

/**
 * Flowmix 保活前台服务
 * 
 * 功能：
 * 1. 通过前台服务机制保持应用在后台运行
 * 2. 维持FlowSync设备监听功能
 * 3. 保持AutoEq音频效果处理能力
 * 4. 显示动态状态通知
 */
class FlowmixKeepAliveService : Service() {

    companion object {
        private const val TAG = "FlowmixKeepAliveService"
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "flowmix_keep_alive"
        private const val CHANNEL_NAME = "Flowmix 保活服务"
        
        // 服务状态
        private var isServiceRunning = false
        
        /**
         * 启动保活服务
         */
        fun startService(context: Context, isFlowmixEnabled: Boolean = false) {
            val intent = Intent(context, FlowmixKeepAliveService::class.java).apply {
                putExtra("flowmix_enabled", isFlowmixEnabled)
                action = "START_KEEP_ALIVE"
            }
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }
        
        /**
         * 停止保活服务
         */
        fun stopService(context: Context) {
            val intent = Intent(context, FlowmixKeepAliveService::class.java).apply {
                action = "STOP_KEEP_ALIVE"
            }
            context.stopService(intent)
        }
        
        /**
         * 更新服务状态
         */
        fun updateServiceStatus(context: Context, isFlowmixEnabled: Boolean) {
            if (isServiceRunning) {
                val intent = Intent(context, FlowmixKeepAliveService::class.java).apply {
                    putExtra("flowmix_enabled", isFlowmixEnabled)
                    action = "UPDATE_STATUS"
                }
                context.startService(intent)
            }
        }
        
        /**
         * 检查服务是否正在运行
         */
        fun isRunning(): Boolean = isServiceRunning
    }

    private var serviceScope: CoroutineScope? = null
    private var notificationManager: NotificationManager? = null
    private var isFlowmixEnabled = false
    
    // FlowSync相关组件
    private var audioDeviceManager: AudioDeviceManager? = null
    private var keepAliveJob: Job? = null

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "FlowmixKeepAliveService 创建")
        
        serviceScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
        notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        
        // 创建通知渠道
        createNotificationChannel()
        
        // 初始化FlowSync组件
        initializeFlowSyncComponents()
        
        isServiceRunning = true
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "onStartCommand: ${intent?.action}")
        
        when (intent?.action) {
            "START_KEEP_ALIVE" -> {
                isFlowmixEnabled = intent.getBooleanExtra("flowmix_enabled", false)
                startForegroundService()
                startKeepAliveMonitoring()
            }
            "UPDATE_STATUS" -> {
                isFlowmixEnabled = intent.getBooleanExtra("flowmix_enabled", false)
                updateNotification()
            }
            "STOP_KEEP_ALIVE" -> {
                stopSelf()
                return START_NOT_STICKY
            }
            else -> {
                // 默认启动
                startForegroundService()
                startKeepAliveMonitoring()
            }
        }
        
        // 返回START_STICKY确保服务被系统杀死后会重启
        return START_STICKY
    }

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "FlowmixKeepAliveService 销毁")
        
        // 清理资源
        keepAliveJob?.cancel()
        serviceScope?.cancel()
        audioDeviceManager?.stopMonitoring()
        
        isServiceRunning = false
    }

    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Flowmix 应用保活服务，维持后台功能运行"
                setShowBadge(false)
                enableLights(false)
                enableVibration(false)
                setSound(null, null)
            }
            notificationManager?.createNotificationChannel(channel)
        }
    }

    /**
     * 启动前台服务
     */
    private fun startForegroundService() {
        val notification = createNotification()
        startForeground(NOTIFICATION_ID, notification)
        Log.d(TAG, "前台服务已启动")
    }

    /**
     * 创建通知
     */
    private fun createNotification(): Notification {
        // 点击通知打开主界面
        val intent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val title = "Flowmix"
        val content = if (isFlowmixEnabled) {
            "Now On    ᖰ(*´▽`*)ᖳ"
        } else {
            "Now Off   ᖰ(´;ω; `)ᖳ"
        }
        
        // 图标直接使用App图标
        val iconRes = R.drawable.ic_flowmix

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(title)
            .setContentText(content)
            .setSmallIcon(iconRes)
            .setContentIntent(pendingIntent)
            .setOngoing(true) // 设置为常驻通知
            .setAutoCancel(false)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setCategory(NotificationCompat.CATEGORY_SERVICE)
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
            .build()
    }

    /**
     * 更新通知
     */
    private fun updateNotification() {
        val notification = createNotification()
        notificationManager?.notify(NOTIFICATION_ID, notification)
        Log.d(TAG, "通知已更新: Flowmix ${if (isFlowmixEnabled) "On" else "Off"}")
    }

    /**
     * 初始化FlowSync组件
     */
    private fun initializeFlowSyncComponents() {
        try {
            // 初始化音频设备管理器用于FlowSync功能
            audioDeviceManager = AudioDeviceManager(this)
            Log.d(TAG, "FlowSync组件初始化完成")
        } catch (e: Exception) {
            Log.e(TAG, "初始化FlowSync组件失败", e)
        }
    }

    /**
     * 启动保活监控
     */
    private fun startKeepAliveMonitoring() {
        keepAliveJob?.cancel()
        keepAliveJob = serviceScope?.launch {
            while (isActive) {
                try {
                    // 每30秒检查一次服务状态
                    delay(30_000)
                    
                    // 确保FlowSync设备监听保持活跃
                    ensureFlowSyncActive()
                    
                    Log.d(TAG, "保活检查完成 - Flowmix: ${if (isFlowmixEnabled) "On" else "Off"}")
                } catch (e: Exception) {
                    Log.e(TAG, "保活监控异常", e)
                }
            }
        }
    }

    /**
     * 确保FlowSync功能保持活跃
     */
    private fun ensureFlowSyncActive() {
        try {
            // 检查音频设备管理器状态
            audioDeviceManager?.let { manager ->
                if (!manager.isMonitoring()) {
                    Log.d(TAG, "重新启动FlowSync设备监听")
                    manager.startMonitoring()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "确保FlowSync活跃状态失败", e)
        }
    }
}
