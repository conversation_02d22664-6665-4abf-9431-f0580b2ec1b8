package cn.ykload.flowmix

import cn.ykload.flowmix.parser.AutoEqParser
import org.junit.Test
import org.junit.Assert.*

/**
 * AutoEq解析器单元测试
 */
class AutoEqParserTest {

    private val parser = AutoEqParser()

    @Test
    fun testValidAutoEqFormat() {
        val validContent = "GraphicEQ: 20 0; 21 0; 22 0; 23 0; 24 0"
        assertTrue("应该识别有效的AutoEq格式", parser.isValidAutoEqFormat(validContent))
    }

    @Test
    fun testInvalidAutoEqFormat() {
        val invalidContent = "This is not an AutoEq file"
        assertFalse("应该拒绝无效的AutoEq格式", parser.isValidAutoEqFormat(invalidContent))
    }

    @Test
    fun testParseSimpleAutoEq() {
        val content = "GraphicEQ: 20 0; 100 -2.5; 1000 3.0; 10000 -1.0"
        val result = parser.parseFromString(content, "Test")
        
        assertNotNull("解析结果不应为null", result)
        assertEquals("配置名称应该正确", "Test", result?.name)
        assertEquals("应该解析出4个频段", 4, result?.bands?.size)
        
        result?.bands?.let { bands ->
            assertEquals("第一个频段频率", 20f, bands[0].frequency)
            assertEquals("第一个频段增益", 0f, bands[0].gain)
            
            assertEquals("第二个频段频率", 100f, bands[1].frequency)
            assertEquals("第二个频段增益", -2.5f, bands[1].gain)
            
            assertEquals("第三个频段频率", 1000f, bands[2].frequency)
            assertEquals("第三个频段增益", 3.0f, bands[2].gain)
            
            assertEquals("第四个频段频率", 10000f, bands[3].frequency)
            assertEquals("第四个频段增益", -1.0f, bands[3].gain)
        }
    }

    @Test
    fun testParseEmptyContent() {
        val result = parser.parseFromString("", "Empty")
        assertNull("空内容应该返回null", result)
    }

    @Test
    fun testParseInvalidContent() {
        val result = parser.parseFromString("Invalid content", "Invalid")
        assertNull("无效内容应该返回null", result)
    }

    @Test
    fun testParseMalformedBands() {
        val content = "GraphicEQ: 20; 100 -2.5; invalid 3.0; 10000 -1.0"
        val result = parser.parseFromString(content, "Malformed")
        
        assertNotNull("解析结果不应为null", result)
        assertEquals("应该只解析出有效的频段", 2, result?.bands?.size)
    }

    @Test
    fun testDataValidation() {
        val content = "GraphicEQ: 20 0; 100 -2.5; 1000 3.0"
        val result = parser.parseFromString(content, "Valid")
        
        assertNotNull("解析结果不应为null", result)
        assertTrue("数据应该有效", result?.isValid() == true)
        
        val (minFreq, maxFreq) = result?.getFrequencyRange() ?: Pair(0f, 0f)
        assertEquals("最小频率", 20f, minFreq)
        assertEquals("最大频率", 1000f, maxFreq)
        
        val (minGain, maxGain) = result?.getGainRange() ?: Pair(0f, 0f)
        assertEquals("最小增益", -2.5f, minGain)
        assertEquals("最大增益", 3.0f, maxGain)
    }
}
